import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/services/helper_services.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';
import 'package:wesell/presentation/widgets/custom_outlined_button.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_shimmer_animation.dart';
import '../blocs/merchant_offer_bloc.dart';

class MerchantOfferingPage extends StatefulWidget {
  const MerchantOfferingPage({super.key});

  @override
  State<MerchantOfferingPage> createState() => _MerchantOfferingPageState();
}

class _MerchantOfferingPageState extends State<MerchantOfferingPage> {

  @override
  void initState() {
    super.initState();
    context.read<MerchantOfferBloc>().add(const GetOfferingsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.offerings),
        elevation: 0,
        actions: [
          //add icon
          IconButton(
            tooltip: "Add Offering",
            icon: const Icon(Icons.add, color: AppColors.primaryTheme),
            onPressed: () {
              AppRoutes.pushScreen(context, AppRoutes.createOffering);
            },
          ),
        ],
      ),
      body: BlocBuilder<MerchantOfferBloc, MerchantOfferState>(
        builder: (context, state) {
          if (state is MerchantOfferLoading) {
            return const Center(child: CircularProgressIndicator(color: AppColors.primaryTheme,));
          } else if (state is MerchantOfferError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${AppStrings.error}${state.message}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<MerchantOfferBloc>().add(const GetOfferingsEvent());
                    },
                    child: Text(AppStrings.retry),
                  ),
                ],
              ),
            );
          } else if (state is MerchantOfferLoaded) {
            if (state.offerings.isEmpty) {
              return Center(child: Text(AppStrings.noDataAvailable));
            }
            return Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: state.offerings.length+ (state.offerings.length > state.totalPages ? 1 : 0),
                    itemBuilder: (context, index) {
                     
                        if (index < state.offerings.length) {
                           final offering = state.offerings[index];
                          return ProductCard(
                          title: offering.title,
                          imageUrl:AppConstants.mediaBaseUrl+offering.mainImage, // You can use offering.mainImage
                          productCount: "${offering.details.services.length} ${AppStrings.products}",
                          priceRange: offering.attributes.rangePerSales.isNotEmpty
                              ? "SAR ${offering.attributes.rangePerSales[0]} - SAR ${offering.attributes.rangePerSales.length > 1 ? offering.attributes.rangePerSales[1] : offering.attributes.rangePerSales[0]}"
                              : AppStrings.priceNotAvailable,
                          onEdit: (id) {
                            // Convert MerchantOffering to CreateOfferingFormData
                            final formdata = CreateOfferingFormData(
                              title: offering.title,
                              description: offering.description,
                              mainImageId: offering.mainImage,
                              averageSellingCycle: offering.details.averageSellingCycle,
                              industry: offering.details.industry,
                              services: offering.details.services.map((s) => ServiceFormData(
                                serviceName: s.serviceName,
                                serviceDescription: s.serviceDescription,
                                minSellingPrice: s.minSellingPrice,
                                maxSellingPrice: s.maxSellingPrice,
                                sku: s.sku,
                                type: s.type,
                                cost: s.cost,
                                serviceImageId: s.serviceImage
                              )).toList(),
                              competitors: offering.details.competitors.map((c) => CompetitorFormData(
                                competitorName: c.competitorName,
                                notes: c.notes,
                                link: c.link
                              )).toList(),
                              previousClients: offering.details.previousClients,
                              faqs: offering.faqs.map((f) => FAQFormData(
                                question: f.question,
                                answer: f.answer,
                                questionUrl: f.questionUrl
                              )).toList(),
                              resources: offering.resources.map((r) => ResourceFormData(
                                resourceTitle: r.resourceTitle,
                                resourceDescription: r.resourceDescription,
                                category: r.category,
                                resourceVideoLink: r.resourceVideoLink,
                                resourceFileId: r.resourceFile
                              )).toList(),
                              rangePerSales: offering.attributes.rangePerSales,
                            );
                            AppRoutes.pushScreen(context, AppRoutes.createOffering, arguments: formdata);
                          },// user offering.offeringId
                          onDelete: (id) {},
                          onAddJob: (id) {},
                        );
                      }else{
                          if (state.hasMoreData){
                            return Padding(
                              padding: const EdgeInsets.all(16.0),
                              child:Container(
                                height: 40,
                                width: 100,
                                alignment: Alignment.center,
                                child: CustomButton(
                                  isLoading: state.isLoadingMore  ,
                                  height: 35,
                                    width: 150,
                                  onPressed: () {
                                    context.read<MerchantOfferBloc>().add(const LoadMoreOfferingsEvent());
                                  },
                                  text: AppStrings.loadMore,
                                  backgroundColor: AppColors.primaryTheme,
                                  textColor: Colors.white,
                                ),
                              ),
                            );
                          }else{
                            return const SizedBox();
                          }
                  
                      }
                      
                    },
                  ),
                ),
              ],
            );
          }
          return Center(child: Text(AppStrings.noDataAvailable));
        },
      ),
    );
  }
}


class ProductCard extends StatelessWidget {
  final String title;
  final String imageUrl;
  final String productCount;
  final String priceRange;
  final  Function(String)? onEdit;
  final  Function(String)? onDelete;
  final  Function(String)? onAddJob;

  const ProductCard({
    super.key,
    required this.title,
    required this.imageUrl,
    required this.productCount,
    required this.priceRange,
    this.onEdit,
    this.onDelete,
    this.onAddJob,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Row: Image + Title & Subtitle
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: Image.network(
                  imageUrl,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                   errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                          color: AppColors.iconLightTheme,
                          alignment: Alignment.center,
                          child: Text(
                            HelperServices.getInitialLetter(title),
                            style: TextStyle(color: Colors.white),
                          ),
                        );
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Shimmer(
                          child: Container(
                            width: 28,
                            height: 28,
                            color: AppColors.iconLightTheme,
                          ),
                        ); // Loading state
                      },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      productCount,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
    
          /// Price Range
          Row(
            children: [
              const Icon(Icons.image, size: 16, color: Colors.grey),
              const SizedBox(width: 6),
              Text(
                AppStrings.rangePerSale,
                style: TextStyle(color: Colors.grey[600]),
              ),
              Text(
                priceRange,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const SizedBox(height: 14),
    
          /// Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomOutlinedButton(
                onPressed: onAddJob != null ? () => onAddJob!("id") : null,
                label:  AppStrings.newJob,
                icon: Icons.add_box_outlined,
                width: 100,
                height: 40,
              ),
              const SizedBox(width: 10),
              Row(
                children: [
                  Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: onEdit != null ? () => onEdit!("id") : null,
                      icon: const Icon(Icons.edit, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: onDelete != null ? () => onDelete!("id") : null,
                      icon: const Icon(Icons.delete_outline, color: Colors.grey),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          Divider(color:AppColors.borderTheme,thickness: 1,),
        ],
      ),
    );
  }
}

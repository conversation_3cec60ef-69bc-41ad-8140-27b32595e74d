class CreateOfferingFormData {
  // Basic Info
  String title;
  String description;
  String? mainImageId;
  
  // Details (will be populated in step 2)
  List<int> averageSellingCycle;
  List<String> industry;
  List<ServiceFormData> services;
  List<CompetitorFormData> competitors;
  List<String> previousClients;
  
  // FAQ (will be populated in step 3)
  List<FAQFormData> faqs;
  
  // Resources (will be populated in step 4)
  List<ResourceFormData> resources;
  
  // Attributes
  String createdBy;
  List<int> rangePerSales;

  CreateOfferingFormData({
    this.title = '',
    this.description = '',
    this.mainImageId,
    this.averageSellingCycle = const [],
    this.industry = const [],
    this.services = const [],
    this.competitors = const [],
    this.previousClients = const [],
    this.faqs = const [],
    this.resources = const [],
    this.createdBy = '',
    this.rangePerSales = const [],
  });

  // Convert to API request format
  Map<String, dynamic> toApiRequest() {
    return {
      "title": title,
      "description": description,
      "details": {
        "averageSellingCycle": averageSellingCycle,
        "industry": industry,
        "services": services.map((service) => service.toJson()).toList(),
        "competitors": competitors.map((competitor) => competitor.toJson()).toList(),
        "previousClients": previousClients,
      },
      "FAQs": faqs.map((faq) => faq.toJson()).toList(),
      "resources": resources.map((resource) => resource.toJson()).toList(),
      "attributes": {
        "createdBy": createdBy,
        "rangePerSales": rangePerSales,
      },
      "mainImage": mainImageId ?? '',
    };
  }

  // Validation methods
  bool isBasicInfoValid() {
    return title.isNotEmpty && 
           description.isNotEmpty && 
           description.length <= 500 &&
           mainImageId != null && 
           mainImageId!.isNotEmpty;
  }

  bool isDetailsValid() {
    return averageSellingCycle.length == 2 &&
           averageSellingCycle[0] > 0 &&
           averageSellingCycle[1] > 0 &&
           averageSellingCycle[0] <= averageSellingCycle[1] &&
           rangePerSales.length == 2 &&
           rangePerSales[0] > 0 &&
           rangePerSales[1] > 0 &&
           rangePerSales[0] <= rangePerSales[1] &&
           industry.isNotEmpty;
  }

  bool isFAQsValid() {
    return faqs.isNotEmpty &&
           faqs.every((faq) => faq.isValid());
  }

  bool isResourcesValid() {
    return resources.isNotEmpty &&
           resources.every((resource) => resource.isValid());
  }

  bool isFormValid() {
    return isBasicInfoValid() && 
           isDetailsValid() && 
           isFAQsValid() && 
           isResourcesValid();
  }
}

class ServiceFormData {
  String serviceName;
  String serviceDescription;
  int minSellingPrice;
  int maxSellingPrice;
  String sku;
  String? type;
  dynamic cost;
  bool needsShipping;
  String? serviceImageId;

  ServiceFormData({
    this.serviceName = '',
    this.serviceDescription = '',
    this.minSellingPrice = 0,
    this.maxSellingPrice = 0,
    this.sku = '',
    this.type,
    this.cost = 0,
    this.needsShipping = false,
    this.serviceImageId,
  });

  bool isValid() {
    return serviceName.isNotEmpty &&
           serviceDescription.isNotEmpty &&
           minSellingPrice > 0 &&
           maxSellingPrice > 0 &&
           maxSellingPrice >= minSellingPrice &&
           sku.isNotEmpty &&
           type!.isNotEmpty &&
           serviceImageId != null &&
           serviceImageId!.isNotEmpty;
  }

  Map<String, dynamic> toJson() {
    return {
      "serviceName": serviceName,
      "serviceDescription": serviceDescription,
      "minSellingPrice": minSellingPrice,
      "maxSellingPrice": maxSellingPrice,
      "sku": sku,
      "type": type,
      "cost": cost,
      "needsShipping": needsShipping,
      "serviceImage": serviceImageId ?? '',
    };
  }
}

class CompetitorFormData {
  String competitorName;
  String notes;
  String link;

  CompetitorFormData({
    this.competitorName = '',
    this.notes = '',
    this.link = '',
  });

  bool isValid() {
    return competitorName.isNotEmpty &&
           notes.isNotEmpty &&
           link.isNotEmpty;
  }

  Map<String, dynamic> toJson() {
    return {
      "competitorName": competitorName,
      "notes": notes,
      "link": link,
    };
  }
}

class FAQFormData {
  String question;
  String answer;
  String questionUrl;

  FAQFormData({
    this.question = '',
    this.answer = '',
    this.questionUrl = '',
  });

  bool isValid() {
    return question.isNotEmpty &&
           answer.isNotEmpty &&
           questionUrl.isNotEmpty;
  }

  Map<String, dynamic> toJson() {
    return {
      "question": question,
      "answer": answer,
      "questionUrl": questionUrl,
    };
  }
}

class ResourceFormData {
  String resourceTitle;
  String resourceDescription;
  String category;
  String resourceVideoLink;
  String? resourceFileId;

  ResourceFormData({
    this.resourceTitle = '',
    this.resourceDescription = '',
    this.category = '',
    this.resourceVideoLink = '',
    this.resourceFileId,
  });

  bool isValid() {
    return resourceTitle.isNotEmpty &&
           resourceDescription.isNotEmpty &&
           category.isNotEmpty &&
           resourceVideoLink.isNotEmpty &&
           resourceFileId != null &&
           resourceFileId!.isNotEmpty;
  }

  Map<String, dynamic> toJson() {
    return {
      "resourceTitle": resourceTitle,
      "resourceDescription": resourceDescription,
      "category": category,
      "resourceVideoLink": resourceVideoLink,
      "resourceFile": resourceFileId ?? '',
    };
  }
}

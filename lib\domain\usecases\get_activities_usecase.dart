import 'package:dartz/dartz.dart';
import 'package:wesell/core/error/failures.dart';
import 'package:wesell/data/models/activity_model.dart';
import 'package:wesell/domain/repositories/merchant_counter_repository.dart';

class GetActivities {
  final MerchantStatsRepository repository;

  GetActivities(this.repository);

  Future<Either<Failure, ActivitiesResponse>> call() async {
    return await repository.getActivities();
  }
}

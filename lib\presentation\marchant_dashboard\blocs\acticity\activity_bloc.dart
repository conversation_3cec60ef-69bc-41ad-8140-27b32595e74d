import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/data/models/activity_model.dart';
import 'package:wesell/domain/usecases/get_activities_usecase.dart';

part 'activity_event.dart';
part 'activity_state.dart';

class ActivityBloc extends Bloc<ActivityEvent, ActivityState> {
  final GetActivities getActivities;

  ActivityBloc({
    required this.getActivities,
  }) : super(ActivitiesInitial()) {
    on<GetActivitiesEvent>(_onGetActivities);
  }


  Future<void> _onGetActivities(
    GetActivitiesEvent event,
    Emitter<ActivityState> emit,
  ) async {
    emit(ActivitiesLoading());

    final result = await getActivities();

    result.fold(
      (failure) => emit(ActivitiesError(message: failure.message)),
      (response) => emit(ActivitiesLoaded(activities: response.rows ?? [])),
    );
  }
}
part of 'activity_bloc.dart';

abstract class ActivityState extends Equatable {
  const ActivityState();

  @override
  List<Object> get props => [];
}

class ActivitiesInitial extends ActivityState {}

class ActivitiesLoading extends ActivityState {}

class ActivitiesLoaded extends ActivityState {
  final List<ActivityModel> activities;

  const ActivitiesLoaded({required this.activities});

  @override
  List<Object> get props => [activities];
}

class ActivitiesError extends ActivityState {
  final String message;

  const ActivitiesError({required this.message});

  @override
  List<Object> get props => [message];
}

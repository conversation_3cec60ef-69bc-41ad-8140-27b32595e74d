import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/merchant_offering_model.dart';
import '../usecases/create_offering_usecase.dart';

abstract class MerchantOfferingRepository {
  Future<Either<Failure, MerchantOfferingResponse>> getMerchantOfferings({
    required int page,
    required int size,
  });

  Future<Either<Failure, CreateOfferingResponse>> createOffering(Map<String, dynamic> offeringData);
}

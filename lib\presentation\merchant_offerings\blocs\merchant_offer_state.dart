part of 'merchant_offer_bloc.dart';

abstract class MerchantOfferState extends Equatable {
  const MerchantOfferState();

  @override
  List<Object> get props => [];
}

class MerchantOfferInitial extends MerchantOfferState {}

class MerchantOfferLoading extends MerchantOfferState {}

class MerchantOfferLoaded extends MerchantOfferState {
  final List<MerchantOffering> offerings;
  final int currentPage;
  final int totalPages;
  final bool hasMoreData;
  final bool isLoadingMore;

  const MerchantOfferLoaded({
    required this.offerings,
    required this.currentPage,
    required this.totalPages,
    required this.hasMoreData,
    this.isLoadingMore = false,
  });

  @override
  List<Object> get props => [offerings, currentPage, totalPages, hasMoreData, isLoadingMore];

  MerchantOfferLoaded copyWith({
    List<MerchantOffering>? offerings,
    int? currentPage,
    int? totalPages,
    bool? hasMoreData,
    bool? isLoadingMore,
  }) {
    return MerchantOfferLoaded(
      offerings: offerings ?? this.offerings,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
}

class MerchantOfferError extends MerchantOfferState {
  final String message;

  const MerchantOfferError({required this.message});

  @override
  List<Object> get props => [message];
}

class MerchantOfferCreating extends MerchantOfferState {}

class MerchantOfferCreated extends MerchantOfferState {
  final String id;
  final String message;

  const MerchantOfferCreated({
    required this.id,
    required this.message,
  });

  @override
  List<Object> get props => [id, message];
}

class MerchantOfferCreateError extends MerchantOfferState {
  final String message;

  const MerchantOfferCreateError({required this.message});

  @override
  List<Object> get props => [message];
}

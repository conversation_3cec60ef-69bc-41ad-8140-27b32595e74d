class MerchantOfferingResponse {
  final List<MerchantOffering> rows;
  final int total;
  final int size;
  final int page;

  MerchantOfferingResponse({
    required this.rows,
    required this.total,
    required this.size,
    required this.page,
  });

  factory MerchantOfferingResponse.fromJson(Map<String, dynamic> json) {
    return MerchantOfferingResponse(
      rows: (json['rows'] as List<dynamic>?)
          ?.map((item) => MerchantOffering.fromJson(item))
          .toList() ?? [],
      total: json['total'] ?? 0,
      size: json['size'] ?? 0,
      page: json['page'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rows': rows.map((item) => item.toJson()).toList(),
      'total': total,
      'size': size,
      'page': page,
    };
  }
}

class MerchantOffering {
  final String id;
  final String userId;
  final String title;
  final String description;
  final String mainImage;
  final OfferingDetails details;
  final List<FAQ> faqs;
  final List<Resource> resources;
  final OfferingAttributes attributes;
  final String offeringId;
  final String createdAt;
  final String updatedAt;
  final int version;

  MerchantOffering({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.mainImage,
    required this.details,
    required this.faqs,
    required this.resources,
    required this.attributes,
    required this.offeringId,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
  });

  factory MerchantOffering.fromJson(Map<String, dynamic> json) {
    return MerchantOffering(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      mainImage: json['mainImage'] ?? '',
      details: OfferingDetails.fromJson(json['details'] ?? {}),
      faqs: (json['FAQs'] as List<dynamic>?)
          ?.map((item) => FAQ.fromJson(item))
          .toList() ?? [],
      resources: (json['resources'] as List<dynamic>?)
          ?.map((item) => Resource.fromJson(item))
          .toList() ?? [],
      attributes: OfferingAttributes.fromJson(json['attributes'] ?? {}),
      offeringId: json['offeringId'] ?? '',
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      version: json['__v'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'title': title,
      'description': description,
      'mainImage': mainImage,
      'details': details.toJson(),
      'FAQs': faqs.map((item) => item.toJson()).toList(),
      'resources': resources.map((item) => item.toJson()).toList(),
      'attributes': attributes.toJson(),
      'offeringId': offeringId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      '__v': version,
    };
  }
}

class OfferingDetails {
  final List<int> averageSellingCycle;
  final List<String> industry;
  final List<Service> services;
  final List<Competitor> competitors;
  final List<String> previousClients;

  OfferingDetails({
    required this.averageSellingCycle,
    required this.industry,
    required this.services,
    required this.competitors,
    required this.previousClients,
  });

  factory OfferingDetails.fromJson(Map<String, dynamic> json) {
    return OfferingDetails(
      averageSellingCycle: (json['averageSellingCycle'] as List<dynamic>?)
          ?.map((item) => item as int)
          .toList() ?? [],
      industry: (json['industry'] as List<dynamic>?)
          ?.map((item) => item.toString())
          .toList() ?? [],
      services: (json['services'] as List<dynamic>?)
          ?.map((item) => Service.fromJson(item))
          .toList() ?? [],
      competitors: (json['competitors'] as List<dynamic>?)
          ?.map((item) => Competitor.fromJson(item))
          .toList() ?? [],
      previousClients: (json['previousClients'] as List<dynamic>?)
          ?.map((item) => item.toString())
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'averageSellingCycle': averageSellingCycle,
      'industry': industry,
      'services': services.map((item) => item.toJson()).toList(),
      'competitors': competitors.map((item) => item.toJson()).toList(),
      'previousClients': previousClients,
    };
  }
}

class Service {
  final String serviceName;
  final String serviceDescription;
  final int minSellingPrice;
  final int maxSellingPrice;
  final String sku;
  final String type;
  final dynamic cost;
  final bool? needsShipping;
  final String serviceImage;

  Service({
    required this.serviceName,
    required this.serviceDescription,
    required this.minSellingPrice,
    required this.maxSellingPrice,
    required this.sku,
    required this.type,
    required this.cost,
    this.needsShipping,
    required this.serviceImage,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      serviceName: json['serviceName'] ?? '',
      serviceDescription: json['serviceDescription'] ?? '',
      minSellingPrice: json['minSellingPrice'] ?? 0,
      maxSellingPrice: json['maxSellingPrice'] ?? 0,
      sku: json['sku'] ?? '',
      type: json['type'] ?? '',
      cost: json['cost'] ?? 0,
      needsShipping: json['needsShipping'],
      serviceImage: json['serviceImage'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceName': serviceName,
      'serviceDescription': serviceDescription,
      'minSellingPrice': minSellingPrice,
      'maxSellingPrice': maxSellingPrice,
      'sku': sku,
      'type': type,
      'cost': cost,
      'needsShipping': needsShipping,
      'serviceImage': serviceImage,
    };
  }
}

class Competitor {
  final String competitorName;
  final String notes;
  final String link;

  Competitor({
    required this.competitorName,
    required this.notes,
    required this.link,
  });

  factory Competitor.fromJson(Map<String, dynamic> json) {
    return Competitor(
      competitorName: json['competitorName'] ?? '',
      notes: json['notes'] ?? '',
      link: json['link'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'competitorName': competitorName,
      'notes': notes,
      'link': link,
    };
  }
}

class FAQ {
  final String question;
  final String answer;
  final String questionUrl;

  FAQ({
    required this.question,
    required this.answer,
    required this.questionUrl,
  });

  factory FAQ.fromJson(Map<String, dynamic> json) {
    return FAQ(
      question: json['question'] ?? '',
      answer: json['answer'] ?? '',
      questionUrl: json['questionUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'answer': answer,
      'questionUrl': questionUrl,
    };
  }
}

class Resource {
  final String resourceTitle;
  final String resourceDescription;
  final String category;
  final String resourceFile;
  final String resourceVideoLink;

  Resource({
    required this.resourceTitle,
    required this.resourceDescription,
    required this.category,
    required this.resourceFile,
    required this.resourceVideoLink,
  });

  factory Resource.fromJson(Map<String, dynamic> json) {
    return Resource(
      resourceTitle: json['resourceTitle'] ?? '',
      resourceDescription: json['resourceDescription'] ?? '',
      category: json['category'] ?? '',
      resourceFile: json['resourceFile'] ?? '',
      resourceVideoLink: json['resourceVideoLink'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'resourceTitle': resourceTitle,
      'resourceDescription': resourceDescription,
      'category': category,
      'resourceFile': resourceFile,
      'resourceVideoLink': resourceVideoLink,
    };
  }
}

class OfferingAttributes {
  final String? createdBy;
  final List<int> rangePerSales;

  OfferingAttributes({
    this.createdBy,
    required this.rangePerSales,
  });

  factory OfferingAttributes.fromJson(Map<String, dynamic> json) {
    return OfferingAttributes(
      createdBy: json['createdBy'],
      rangePerSales: (json['rangePerSales'] as List<dynamic>?)
          ?.map((item) => item as int)
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'rangePerSales': rangePerSales,
    };
  }
}

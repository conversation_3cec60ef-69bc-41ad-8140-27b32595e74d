import 'package:dartz/dartz.dart';
import 'package:wesell/core/error/failures.dart';
import 'package:wesell/data/models/merchant_counter_model.dart';
import 'package:wesell/domain/repositories/merchant_counter_repository.dart';

class GetMerchantStats {
  final MerchantStatsRepository repository;

  GetMerchantStats(this.repository);

  Future<Either<Failure, MerchantStats>> call(String duration) async {
    return await repository.getMerchantStats(duration);
  }
}

import 'package:bloc/bloc.dart';
import '../../../domain/usecases/get_profile_usecase.dart';
import '../../../domain/usecases/upload_file_usecase.dart';
import '../../../domain/usecases/update_company_details_usecase.dart';
import '../../../domain/usecases/update_profile_progress_usecase.dart';
import '../../../domain/usecases/update_industry_details_usecase.dart';
import '../../../domain/usecases/update_company_locations_usecase.dart';
import '../../../domain/usecases/update_digital_information_usecase.dart';
import '../../../domain/usecases/update_legal_documents_usecase.dart';
import '../../../domain/usecases/update_bank_details_usecase.dart';
import '../../../domain/usecases/get_countries_usecase.dart';
import '../../../domain/usecases/get_cities_usecase.dart';
import '../../../domain/usecases/get_industries_usecase.dart';
import '../../../domain/usecases/get_file_metadata_usecase.dart';
import 'profile_marchant_event.dart';
import 'profile_marchant_state.dart';

class ProfileMarchantBloc extends Bloc<ProfileMarchantEvent, ProfileMarchantState> {
  final GetProfileUseCase getProfileUseCase;
  final UploadFileUseCase uploadFileUseCase;
  final UpdateCompanyDetailsUseCase updateCompanyDetailsUseCase;
  final UpdateProfileProgressUseCase updateProfileProgressUseCase;
  final UpdateIndustryDetailsUseCase updateIndustryDetailsUseCase;
  final UpdateCompanyLocationsUseCase updateCompanyLocationsUseCase;
  final UpdateDigitalInformationUseCase updateDigitalInformationUseCase;
  final UpdateLegalDocumentsUseCase updateLegalDocumentsUseCase;
  final UpdateBankDetailsUseCase updateBankDetailsUseCase;
  final GetCountriesUseCase getCountriesUseCase;
  final GetCitiesUseCase getCitiesUseCase;
  final GetIndustriesUseCase getIndustriesUseCase;
  final GetFileMetadataUseCase getFileMetadataUseCase;

  ProfileMarchantBloc({
    required this.getProfileUseCase,
    required this.uploadFileUseCase,
    required this.updateCompanyDetailsUseCase,
    required this.updateProfileProgressUseCase,
    required this.updateIndustryDetailsUseCase,
    required this.updateCompanyLocationsUseCase,
    required this.updateDigitalInformationUseCase,
    required this.updateLegalDocumentsUseCase,
    required this.updateBankDetailsUseCase,
    required this.getCountriesUseCase,
    required this.getCitiesUseCase,
    required this.getIndustriesUseCase,
    required this.getFileMetadataUseCase,
  }) : super(ProfileMarchantInitial()) {
    on<GetProfileEvent>(_onGetProfile);
    on<UpdateProfileProgressEvent>(_onUpdateProfileProgress);
    on<UploadImageEvent>(_onUploadImage);
    on<UpdateCompanyDetailsEvent>(_onUpdateCompanyDetails);
    on<UpdateIndustryDetailsEvent>(_onUpdateIndustryDetails);
    on<UpdateCompanyLocationsEvent>(_onUpdateCompanyLocations);
    on<UpdateDigitalInformationEvent>(_onUpdateDigitalInformation);
    on<UpdateLegalDocumentsEvent>(_onUpdateLegalDocuments);
    on<UpdateBankDetailsEvent>(_onUpdateBankDetails);
    on<GetCountriesEvent>(_onGetCountries);
    on<GetCitiesEvent>(_onGetCities);
    on<GetIndustriesEvent>(_onGetIndustries);
    on<GetFileMetadataEvent>(_onGetFileMetadata);
  }

  Future<void> _onGetProfile(GetProfileEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(ProfileMarchantLoading());
    try {
      final result = await getProfileUseCase();
      result.fold(
        (failure) => emit(ProfileMarchantError(message: failure.message)),
        (profile) => emit(ProfileMarchantLoaded(profile: profile)),
      );
    } catch (e) {
      emit(ProfileMarchantError(message: e.toString()));
    }
  }

  Future<void> _onUploadImage(UploadImageEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(ImageUploadLoading(  uploadId: event.uploadId));
    try {
      final result = await uploadFileUseCase(UploadFileParams(
        file: event.file,
        fileName: event.fileName,
      ));
      result.fold(
        (failure) => emit(ImageUploadError(message: failure.message, uploadId: event.uploadId)),
        (response) => emit(ImageUploadSuccess(
          fileId: response.fileId,
          fileName: response.fileName,
          uploadId: event.uploadId,
        )),
      );
    } catch (e) {
      emit(ImageUploadError(message: e.toString(), uploadId: event.uploadId));
    }
  }

  Future<void> _onUpdateCompanyDetails(UpdateCompanyDetailsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateCompanyDetailsLoading());
    try {
      final result = await updateCompanyDetailsUseCase(UpdateCompanyDetailsParams(
        companyName: event.companyName,
        logo: event.logo,
        description: event.description,
        detailedDescription: event.detailedDescription,
      ));
      result.fold(
        (failure) => emit(UpdateCompanyDetailsError(message: failure.message)),
        (response) => emit(UpdateCompanyDetailsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateCompanyDetailsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateProfileProgress(
    UpdateProfileProgressEvent event,
    Emitter<ProfileMarchantState> emit,
  ) async {
    emit(UpdateProfileProgressLoading());
    try {
      final result = await updateProfileProgressUseCase(event.profileProgress);
      result.fold(
        (failure) => emit(UpdateProfileProgressError(message: failure.message)),
        (response) => emit(UpdateProfileProgressSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateProfileProgressError(message: e.toString()));
    }
  }

  Future<void> _onUpdateIndustryDetails(UpdateIndustryDetailsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateIndustryDetailsLoading());
    try {
      final result = await updateIndustryDetailsUseCase(UpdateIndustryDetailsParams(
        industries: event.industries,
      ));
      result.fold(
        (failure) => emit(UpdateIndustryDetailsError(message: failure.message)),
        (response) => emit(UpdateIndustryDetailsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateIndustryDetailsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateCompanyLocations(UpdateCompanyLocationsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateCompanyLocationsLoading());
    try {
      final result = await updateCompanyLocationsUseCase(UpdateCompanyLocationsParams(
        country: event.country,
        city: event.city,
        address: event.address,
        poBox: event.poBox,
      ));
      result.fold(
        (failure) => emit(UpdateCompanyLocationsError(message: failure.message)),
        (response) => emit(UpdateCompanyLocationsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateCompanyLocationsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateDigitalInformation(UpdateDigitalInformationEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateDigitalInformationLoading());
    try {
      final result = await updateDigitalInformationUseCase(UpdateDigitalInformationParams(
        website: event.website,
        facebook: event.facebook,
        twitter: event.twitter,
        linkedin: event.linkedin,
        youtube: event.youtube,
        tiktok: event.tiktok,
        snapchat: event.snapchat,
      ));
      result.fold(
        (failure) => emit(UpdateDigitalInformationError(message: failure.message)),
        (response) => emit(UpdateDigitalInformationSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateDigitalInformationError(message: e.toString()));
    }
  }

  Future<void> _onUpdateLegalDocuments(UpdateLegalDocumentsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateLegalDocumentsLoading());
    try {
      final result = await updateLegalDocumentsUseCase(UpdateLegalDocumentsParams(
        companyLegalName: event.companyLegalName,
        companySize: event.companySize,
        crNumber: event.crNumber,
        investedCapital: event.investedCapital,
        investedCapitalUnit: event.investedCapitalUnit,
        crDocument: event.crDocument,
        licenseDocument: event.licenseDocument,
      ));
      result.fold(
        (failure) => emit(UpdateLegalDocumentsError(message: failure.message)),
        (response) => emit(UpdateLegalDocumentsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateLegalDocumentsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateBankDetails(UpdateBankDetailsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateBankDetailsLoading());
    try {
      final result = await updateBankDetailsUseCase(UpdateBankDetailsParams(
        iban: event.iban,
        accountName: event.accountName,
        bankName: event.bankName,
        ibanCertificate: event.ibanCertificate,
      ));
      result.fold(
        (failure) => emit(UpdateBankDetailsError(message: failure.message)),
        (response) => emit(UpdateBankDetailsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateBankDetailsError(message: e.toString()));
    }
  }

  Future<void> _onGetCountries(GetCountriesEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(CountriesLoading());
    try {
      final result = await getCountriesUseCase();
      result.fold(
        (failure) => emit(CountriesError(message: failure.message)),
        (response) => emit(CountriesLoaded(countries: response.rows)),
      );
    } catch (e) {
      emit(CountriesError(message: e.toString()));
    }
  }

  Future<void> _onGetCities(GetCitiesEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(CitiesLoading());
    try {
      final result = await getCitiesUseCase(event.countryId);
      result.fold(
        (failure) => emit(CitiesError(message: failure.message)),
        (response) => emit(CitiesLoaded(cities: response.rows)),
      );
    } catch (e) {
      emit(CitiesError(message: e.toString()));
    }
  }

  Future<void> _onGetIndustries(GetIndustriesEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(IndustriesLoading());
    try {
      final result = await getIndustriesUseCase();
      result.fold(
        (failure) => emit(IndustriesError(message: failure.message)),
        (response) => emit(IndustriesLoaded(industries: response.rows)),
      );
    } catch (e) {
      emit(IndustriesError(message: e.toString()));
    }
  }

  Future<void> _onGetFileMetadata(GetFileMetadataEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(FileMetadataLoading(fileId: event.fileId));
    try {
      final result = await getFileMetadataUseCase(event.fileId);
      result.fold(
        (failure) => emit(FileMetadataError(fileId: event.fileId, message: failure.message)),
        (response) => emit(FileMetadataLoaded(
          fileId: event.fileId,
          name: response['name'] ?? '',
          ext: response['ext'] ?? '',
          sizeInBytes: response['sizeInBytes'] ?? 0,
        )),
      );
    } catch (e) {
      emit(FileMetadataError(fileId: event.fileId, message: e.toString()));
    }
  }
}

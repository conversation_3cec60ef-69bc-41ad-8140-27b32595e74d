import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/login_response.dart';
import '../models/register_response.dart';

abstract class AuthRemoteDataSource {
  Future<LoginResponse> login({
    required String email,
    required String password,
  });

  Future<LoginResponse> refreshToken({
    required String refreshToken,
  });

  Future<RegisterResponse> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String type,
    // Merchant fields (optional)
    String? companyName,
    String? companyLegalName,
    String? crNumber,
    int? investedCapital,
    String? investedCapitalUnit,
    String? companySize,
    String? licenseDocument,
    String? crDocument,
  });

  Future<Map<String, dynamic>> forgotPassword({
    required String email,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioClient dioClient;

  AuthRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<LoginResponse> login({
    required String email,
    required String password,
  }) async {
    final response = await dioClient.post(
      AppConstants.loginEndpoint,
      data: {'email': email, 'password': password},
      queryParameters: {'isMobile': true},
    );
    return LoginResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<LoginResponse> refreshToken({
    required String refreshToken,
  }) async {
    final response = await dioClient.post(
      AppConstants.refreshTokenEndpoint,
      data: {
        'refreshToken': refreshToken,
      },
    );
    return LoginResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<RegisterResponse> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String type,
    String? companyName,
    String? companyLegalName,
    String? crNumber,
    int? investedCapital,
    String? investedCapitalUnit,
    String? companySize,
    String? licenseDocument,
    String? crDocument,
  }) async {
    final response = await dioClient.post(
      AppConstants.registerEndpoint,
        data: {
          'firstName': firstName,
          'lastName': lastName,
          'email': email,
          'password': password,
          'type': type,
          "companyName": companyName ?? "",
          "attributes": {
              "company": {
              "companyLegalName":companyLegalName ?? "",
              "crNumber": crNumber ?? "",
              "investedCapital": investedCapital ?? 0,
              "investedCapitalUnit": investedCapitalUnit ?? "",
              "companySize": companySize ?? "",
              "licenseDocument": licenseDocument ?? "", // Use the media api to upload the file and pass the id here
              "crDocument": crDocument ?? "" // Use the media api to upload the file and pass the id here
              }
          }
      },
      queryParameters: {'isMobile': true},
    );
    return RegisterResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<Map<String, dynamic>> forgotPassword({
    required String email,
  }) async {
    final response = await dioClient.post(
      AppConstants.forgotPasswordEndpoint,
      queryParameters: {'email': email},
    );
    return response.data as Map<String, dynamic>;
  }

}
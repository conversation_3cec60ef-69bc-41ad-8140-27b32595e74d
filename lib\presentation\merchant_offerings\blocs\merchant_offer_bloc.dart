import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/models/merchant_offering_model.dart';
import '../../../domain/usecases/get_merchant_offerings_usecase.dart';
import '../../../domain/usecases/create_offering_usecase.dart';

part 'merchant_offer_event.dart';
part 'merchant_offer_state.dart';

class MerchantOfferBloc extends Bloc<MerchantOfferEvent, MerchantOfferState> {
  final GetMerchantOfferingsUseCase getMerchantOfferingsUseCase;
  final CreateOfferingUseCase createOfferingUseCase;

  MerchantOfferBloc({
    required this.getMerchantOfferingsUseCase,
    required this.createOfferingUseCase,
  }) : super(MerchantOfferInitial()) {
    on<GetOfferingsEvent>(_onGetOfferings);
    on<LoadMoreOfferingsEvent>(_onLoadMoreOfferings);
    on<CreateOfferingEvent>(_onCreateOffering);
  }

  Future<void> _onGetOfferings(
    GetOfferingsEvent event,
    Emitter<MerchantOfferState> emit,
  ) async {
    if (event.isRefresh) {
      emit(MerchantOfferLoading());
    } else {
      emit(MerchantOfferLoading());
    }

    final result = await getMerchantOfferingsUseCase(
      page: event.page,
      size: event.size,
    );

    result.fold(
      (failure) => emit(MerchantOfferError(message: failure.message)),
      (response) {
        final totalPages = (response.total / response.size).ceil();
        final hasMoreData = event.page < totalPages;

        emit(MerchantOfferLoaded(
          offerings: response.rows,
          currentPage: response.page,
          totalPages: totalPages,
          hasMoreData: hasMoreData,
        ));
      },
    );
  }

  Future<void> _onLoadMoreOfferings(
    LoadMoreOfferingsEvent event,
    Emitter<MerchantOfferState> emit,
  ) async {
    final currentState = state;
    if (currentState is MerchantOfferLoaded && currentState.hasMoreData && !currentState.isLoadingMore) {
      emit(currentState.copyWith(isLoadingMore: true));

      final nextPage = currentState.currentPage + 1;
      final result = await getMerchantOfferingsUseCase(
        page: nextPage,
        size: 9,
      );

      result.fold(
        (failure) => emit(MerchantOfferError(message: failure.message)),
        (response) {
          final totalPages = (response.total / response.size).ceil();
          final hasMoreData = nextPage < totalPages;
          final updatedOfferings = [...currentState.offerings, ...response.rows];

          emit(MerchantOfferLoaded(
            offerings: updatedOfferings,
            currentPage: response.page,
            totalPages: totalPages,
            hasMoreData: hasMoreData,
            isLoadingMore: false,
          ));
        },
      );
    }
  }

  Future<void> _onCreateOffering(
    CreateOfferingEvent event,
    Emitter<MerchantOfferState> emit,
  ) async {
    emit(MerchantOfferCreating());

    final result = await createOfferingUseCase(event.offeringData);

    result.fold(
      (failure) => emit(MerchantOfferCreateError(message: failure.message)),
      (response) {
        emit(MerchantOfferCreated(
          id: response.id,
          message: response.message,
        ));
        // After successful creation, refresh the offerings list
        add(const GetOfferingsEvent(isRefresh: true));
      },
    );
  }
}

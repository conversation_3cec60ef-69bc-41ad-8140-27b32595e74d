part of 'merchant_offer_bloc.dart';

abstract class MerchantOfferEvent extends Equatable {
  const MerchantOfferEvent();

  @override
  List<Object> get props => [];
}

class GetOfferingsEvent extends MerchantOfferEvent {
  final int page;
  final int size;
  final bool isRefresh;

  const GetOfferingsEvent({
    this.page = 1,
    this.size = 9,
    this.isRefresh = false,
  });

  @override
  List<Object> get props => [page, size, isRefresh];
}

class LoadMoreOfferingsEvent extends MerchantOfferEvent {
  const LoadMoreOfferingsEvent();
}

class CreateOfferingEvent extends MerchantOfferEvent {
  final Map<String, dynamic> offeringData;

  const CreateOfferingEvent({required this.offeringData});

  @override
  List<Object> get props => [offeringData];
}

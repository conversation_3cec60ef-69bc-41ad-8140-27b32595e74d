import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/repositories/merchant_offering_repository.dart';
import '../../domain/usecases/create_offering_usecase.dart';
import '../datasources/merchant_offering_remote_data_source.dart';
import '../models/merchant_offering_model.dart';

class MerchantOfferingRepositoryImpl implements MerchantOfferingRepository {
  final MerchantOfferingRemoteDataSource remoteDataSource;

  MerchantOfferingRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, MerchantOfferingResponse>> getMerchantOfferings({
    required int page,
    required int size,
  }) async {
    try {
      final response = await remoteDataSource.getMerchantOfferings(
        page: page,
        size: size,
      );
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CreateOfferingResponse>> createOffering(Map<String, dynamic> offeringData) async {
    try {
      final response = await remoteDataSource.createOffering(offeringData);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}

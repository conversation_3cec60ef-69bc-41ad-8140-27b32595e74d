part of 'merchant_counter_bloc.dart';

abstract class MerchantStatsEvent extends Equatable {
  const MerchantStatsEvent();

  @override
  List<Object> get props => [];
}

class GetMerchantStatsEvent extends MerchantStatsEvent {
  final String duration;

  const GetMerchantStatsEvent({required this.duration});

  @override
  List<Object> get props => [duration];
}

class GetActivitiesEvent extends MerchantStatsEvent {
  const GetActivitiesEvent();
}

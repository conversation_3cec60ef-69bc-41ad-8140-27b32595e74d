import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:step_progress/step_progress.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/presentation/merchant_offerings/widgets/resources_list_view.dart';
import 'package:wesell/presentation/merchant_offerings/blocs/merchant_offer_bloc.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_outlined_button.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import 'package:wesell/presentation/widgets/chip_input_widget.dart';
import 'package:wesell/presentation/merchant_offerings/widgets/add_faq_list_view.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class CreateOfferingPage extends StatefulWidget {
  final CreateOfferingFormData? formData;
  const CreateOfferingPage({super.key, this.formData});

  @override
  State<CreateOfferingPage> createState() => _CreateOfferingPageState();
}

class _CreateOfferingPageState extends State<CreateOfferingPage> {
  final stepProgressController = StepProgressController(totalSteps: 5);
  int currentStep = 0;
  CreateOfferingFormData formData = CreateOfferingFormData();

  @override
  void initState() {
    super.initState();
    if(widget.formData != null) {
      formData = widget.formData!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<MerchantOfferBloc, MerchantOfferState>(
      listener: (context, state) {
        if (state is MerchantOfferCreated) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context); // Go back to offerings list
        } else if (state is MerchantOfferCreateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
        backgroundColor: AppColors.whiteTheme,
        appBar: AppBar(
          backgroundColor: AppColors.whiteTheme,
          foregroundColor: Colors.black,
          title: const Text(AppStrings.createOffering),
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              StepProgress(
                totalSteps: 5,
                padding: const EdgeInsets.only( left: 16, right: 16, top: 8, bottom: 8),
                controller: stepProgressController,
                nodeTitles:  [
                  'Basic Info',
                  'Details',
                  'FAQ',
                  'Resources',
                  'Review and Save',
                ],
                theme: const StepProgressThemeData(
                  activeForegroundColor: AppColors.primaryTheme,
                  nodeLabelAlignment: StepLabelAlignment.bottom,
                  stepLineSpacing: 2,
                  stepLineStyle: StepLineStyle(
                    lineThickness: 2,
                  ),
                  nodeLabelStyle: StepLabelStyle (
                    titleStyle: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                onStepChanged: (currentIndex) {
                  setState(() {
                    currentStep = currentIndex;
                  });
                },
              ),
              //create condition widget
              if (stepProgressController.currentStep == 0) ...[
                BasicInfoForm(
                  stepProgressController: stepProgressController,
                  formData: formData,
                  currentStep: currentStep,
                ),
              ] else if (stepProgressController.currentStep == 1) ...[
                DetailsForm( stepProgressController: stepProgressController,
                  formData: formData,
                  currentStep: currentStep,),
              ] else if (stepProgressController.currentStep == 2) ...[
                FAQForm(
                  stepProgressController: stepProgressController,
                  formData: formData,
                  currentStep: currentStep,
                ),
              ] else if (stepProgressController.currentStep == 3) ...[
                ResourcesForm(
                  stepProgressController: stepProgressController,
                  formData: formData,
                  currentStep: currentStep,),
              ] else if (stepProgressController.currentStep == 4) ...[
                ReviewAndSaveForm(
                  stepProgressController: stepProgressController,
                  formData: formData,
                  currentStep: currentStep,
                ),
              ],
            ],
          ),
        ),
      ),
    ));
  }
}

class BasicInfoForm extends StatefulWidget {
  final StepProgressController stepProgressController;
  final CreateOfferingFormData formData;
  final int currentStep;
  final bool? viewOnly;
   const BasicInfoForm({
    super.key,
    required this.stepProgressController,
    required this.formData,
    required this.currentStep,
    this.viewOnly,
  });

  @override
  State<BasicInfoForm> createState() => _BasicInfoFormState();
}

class _BasicInfoFormState extends State<BasicInfoForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  String? mainImageId;

  @override
  void initState() {
    super.initState();
    _titleController.text = widget.formData.title;
    _descriptionController.text = widget.formData.description;
    if(widget.formData.mainImageId != null && widget.formData.mainImageId!.isNotEmpty) mainImageId = widget.formData.mainImageId;
    _titleController.addListener(_syncTitle);
    _descriptionController.addListener(_syncDescription);
  }

  void _syncTitle() {
    widget.formData.title = _titleController.text;
  }

  void _syncDescription() {
    widget.formData.description = _descriptionController.text;
  }

  @override
  void dispose() {
    _titleController.removeListener(_syncTitle);
    _descriptionController.removeListener(_syncDescription);
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24,),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: AppColors.primaryTheme.withValues(alpha: 0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  AppStrings.basicInformation,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryTheme,
                  ),
                ),
                widget.viewOnly == true ?
                CustomButton(
                  height: 30,
                  width: 100,
                  text: 'Edit',
                  onPressed: () {
                    widget.stepProgressController.setCurrentStep(0);
                  },
                ) : const SizedBox.shrink(),
                
              ],
            ),
          ),
        ),
        IgnorePointer(
          ignoring: widget.viewOnly ?? false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24,),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Container(
                color: AppColors.whiteTheme,
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _titleController,
                      hintText: AppStrings.productServiceTitleHint,
                      validator: (value) => Validators.validateRequired(value, AppStrings.productServiceTitle),
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _descriptionController,
                      hintText: AppStrings.productServiceDescription,
                      maxLines: 4,
                      maxLength: 500,
                      validator:  (value) => Validators.validateRequired(value, AppStrings.productServiceDescription),
                    ),
                    const SizedBox(height: 24),
                    FileUploadWidget(
                      label: AppStrings.uploadOfferingMainImage,
                      uploadId: 'offering_main_image',
                      onFileUploaded: (fileId) {
                          setState(() {
                            mainImageId = fileId == '' ? null : fileId;
                            widget.formData.mainImageId = fileId == '' ? null : fileId;
                          });
                        },
                      currentFileId: mainImageId,
                    ),
                    widget.formData.mainImageId != null && widget.formData.mainImageId!.isNotEmpty ?  
                    const SizedBox.shrink():Container(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(top:3.0,left: 16),
                        child: Text("Main Image cannot be empty", style: TextStyle(color: const Color.fromARGB(255, 184, 28, 17),fontSize: 11),),
                      )),
                    const SizedBox(height: 32),
                    widget.viewOnly ==null ?
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomButton(
                          height: 45,
                          width: MediaQuery.of(context).size.width * 0.4,
                          text: AppStrings.previous,
                          onPressed: widget.currentStep == 0 ? null : widget.stepProgressController.previousStep,
                        ),
                        CustomButton(
                          height: 45,
                          width: MediaQuery.of(context).size.width * 0.4,
                          text: AppStrings.next,
                          onPressed: () {
                            if (_formKey.currentState!.validate() && (widget.formData.mainImageId != null && widget.formData.mainImageId!.isNotEmpty)) {
                              widget.stepProgressController.nextStep();
                            }
                          },
                        ),
                      ],
                    ) : const SizedBox.shrink(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class DetailsForm extends StatefulWidget { 
  final CreateOfferingFormData formData;
  final StepProgressController stepProgressController;
  final int currentStep;
  final bool? viewOnly;
  const DetailsForm({
    super.key, 
    required this.formData, 
    required this.stepProgressController, 
    required this.currentStep,
    this.viewOnly,
  });

  @override
  State<DetailsForm> createState() => _DetailsFormState();
}

class _DetailsFormState extends State<DetailsForm> {
  final _formKey = GlobalKey<FormState>();
  final minSellingCycleController = TextEditingController();
  final maxSellingCycleController = TextEditingController();
  final minRangePerSaleController = TextEditingController();
  final maxRangePerSaleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize controllers with existing form data
    if (widget.formData.averageSellingCycle.length == 2) {
      minSellingCycleController.text = widget.formData.averageSellingCycle[0].toString();
      maxSellingCycleController.text = widget.formData.averageSellingCycle[1].toString();
    }
    if (widget.formData.rangePerSales.length == 2) {
      minRangePerSaleController.text = widget.formData.rangePerSales[0].toString();
      maxRangePerSaleController.text = widget.formData.rangePerSales[1].toString();
    }

    // Add listeners to sync with form data
    minSellingCycleController.addListener(_syncSellingCycle);
    maxSellingCycleController.addListener(_syncSellingCycle);
    minRangePerSaleController.addListener(_syncRangePerSale);
    maxRangePerSaleController.addListener(_syncRangePerSale);
  }

  void _syncSellingCycle() {
    final minValue = int.tryParse(minSellingCycleController.text) ?? 0;
    final maxValue = int.tryParse(maxSellingCycleController.text) ?? 0;
    widget.formData.averageSellingCycle = [minValue, maxValue];
  }

  void _syncRangePerSale() {
    final minValue = int.tryParse(minRangePerSaleController.text) ?? 0;
    final maxValue = int.tryParse(maxRangePerSaleController.text) ?? 0;
    widget.formData.rangePerSales = [minValue, maxValue];
  }

  void _onIndustryChanged(List<String> industries) {
    setState(() {
      widget.formData.industry = industries;
    });
  }

  void _onPreviousClientsChanged(List<String> clients) {
    setState(() {
      widget.formData.previousClients = clients;
    });
  }

  String? _validateIndustry( industries) {
    if (widget.formData.industry.isEmpty) {
      return AppConstants.pleaseAddIndustryTag;
    }
    return null;
  }

  @override
  void dispose() {
    minSellingCycleController.removeListener(_syncSellingCycle);
    maxSellingCycleController.removeListener(_syncSellingCycle);
    minRangePerSaleController.removeListener(_syncRangePerSale);
    maxRangePerSaleController.removeListener(_syncRangePerSale);
    minSellingCycleController.dispose();
    maxSellingCycleController.dispose();
    minRangePerSaleController.dispose();
    maxRangePerSaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24,),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: AppColors.primaryTheme.withValues(alpha: 0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  AppStrings.detailsInformation,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryTheme,
                  ),
                ),
                widget.viewOnly == true ?
                CustomButton(
                  height: 30,
                  width: 100,
                  text: 'Edit',
                  onPressed: () {
                    widget.stepProgressController.setCurrentStep(1);
                  },
                ) : const SizedBox.shrink(),
              ],
            ),
          ),
        ),
        IgnorePointer(
          ignoring: widget.viewOnly ?? false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Average Selling Cycle
                  const SizedBox(height: 10),
                  const Text(
                    AppStrings.averageSellingCycle,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(children: [
                    Expanded(
                      child: CustomTextField(
                      controller: minSellingCycleController,
                      hintText: AppStrings.enterMinimumSellingCycle,
                      keyboardType: TextInputType.number,
                      validator: (value) => Validators.validateMinSellingCycle(value, maxSellingCycleController),
                                    ),
                    ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: CustomTextField(
                      controller: maxSellingCycleController,
                      hintText: AppStrings.enterMaximumSellingCycle,
                      keyboardType: TextInputType.number,
                      validator: (value) => Validators.validateMaxSellingCycle(value, minSellingCycleController),
                    ),
                  ),
                  ],),
                  
                  //Range per sales (Minimum and Maximum)
                  const SizedBox(height: 16),
                  const Text(
                    AppStrings.rangePerSales,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: minRangePerSaleController,
                          hintText: AppStrings.enterMinimumRangePerSale,
                          keyboardType: TextInputType.number,
                          validator: (value) => Validators.validateMinRangePerSale(value, maxRangePerSaleController),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: CustomTextField(
                          controller: maxRangePerSaleController,
                          hintText: AppStrings.enterMaximumRangePerSale,
                          keyboardType: TextInputType.number,
                          validator: (value) => Validators.validateMaxRangePerSale(value, minRangePerSaleController),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Industry Chip Input
                  ChipInputWidget(
                    label: AppStrings.productServiceIndustry,
                    hintText: AppStrings.enterIndustryTag,
                    items: widget.formData.industry,
                    onChanged: _onIndustryChanged,
                    validator: _validateIndustry,
                    emptyValidator: _validateIndustry,
                    required: true,
                  ),
                  const SizedBox(height: 24),
                  //Products/Service
                  const Text(
                    AppStrings.productService,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  // const SizedBox(height: 10),
                  ProductServiceScreen(formData: widget.formData),
                  const SizedBox(height: 10),
                  CustomOutlinedButton(
                    height: 35,
                    icon: Icons.add,
                    width: 120,
                    label: AppStrings.addNew,
                    onPressed: () async {
                      final result = await AppRoutes.pushScreen<bool>(
                        context,
                        AppRoutes.addNewService,
                        arguments: {
                          'formData': widget.formData,
                          'serviceToEdit': null,
                          'editIndex': null,
                        },
                      );
          
                      if (result == true) {
                        setState(() {
                          // Refresh the form to show updated services
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                  // Previous Clients Chip Input
                  ChipInputWidget(
                    label: AppStrings.previousClients,
                    hintText: AppStrings.enterClientName,
                    items: widget.formData.previousClients,
                    onChanged: _onPreviousClientsChanged,
                    required: false,
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    AppStrings.competitors,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  // const SizedBox(height: 10),
                  AddCompetitorScreen(formData: widget.formData),
                  const SizedBox(height: 10),
                  CustomOutlinedButton(
                    height: 35,
                    icon: Icons.add,
                    width: 160,
                    label: AppStrings.addCompetitor,
                    onPressed: () async {
                      final result = await AppRoutes.pushScreen<bool>(
                        context,
                        AppRoutes.addCompetitor,
                        arguments: {
                          'formData': widget.formData,
                          'competitorToEdit': null,
                          'editIndex': null,
                        },
                      );
          
                      if (result == true) {
                        setState(() {
                          // Refresh the form to show updated competitors
                        });
                      }
                    },
                  ),
                  //button section
                  const SizedBox(height: 32),
                  widget.viewOnly == null ?
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomButton(
                        height: 45,
                        width: MediaQuery.of(context).size.width * 0.4,
                        text: AppStrings.previous,
                        onPressed: widget.currentStep == 0 ? null : widget.stepProgressController.previousStep,
                      ),
                      CustomButton(
                        height: 45,
                        width: MediaQuery.of(context).size.width * 0.4,
                        text: AppStrings.next,
                        onPressed: () {
                          final isFormValid = _formKey.currentState!.validate();
                          if (isFormValid ) {
                            widget.stepProgressController.nextStep();
                          }
                        },
                      ),
                    ],
                  ) : const SizedBox.shrink(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class FAQForm extends StatefulWidget { 
  final StepProgressController stepProgressController;
  final int currentStep;
  final CreateOfferingFormData formData;
  final bool? viewOnly;
  const FAQForm({
    super.key, 
    required this.stepProgressController, 
    required this.currentStep, 
    required this.formData,
    this.viewOnly,
  });

  @override
  State<FAQForm> createState() => _FAQFormState();
}

class _FAQFormState extends State<FAQForm> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24,),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: AppColors.primaryTheme.withValues(alpha: 0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  AppStrings.faq,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryTheme,
                  ),
                ),
                widget.viewOnly == true ?
                CustomButton(
                  height: 30,
                  width: 100,
                  text: 'Edit',
                  onPressed: () {
                    widget.stepProgressController.setCurrentStep(2);
                  },
                ) : const SizedBox.shrink(),
              ],
            ),
          ),
        ),
        IgnorePointer(
          ignoring: widget.viewOnly ?? false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10),
                AddFaqListView(formData: widget.formData),
                const SizedBox(height: 10),
                CustomOutlinedButton(
                  height: 35,
                  icon: Icons.add,
                  width: 160,
                  label: AppStrings.addNewFAQ,
                  onPressed: () async {
                    final result = await AppRoutes.pushScreen<bool>(
                      context,
                      AppRoutes.addingNewFaq,
                      arguments: {
                        'formData': widget.formData,
                      },
                    );
          
                    if (result == true) {
                      setState(() {
                        // Refresh the form to show updated FAQs
                      });
                    }
                  },
                ),
                const SizedBox(height: 24),
                widget.viewOnly == null ?
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomButton(
                      height: 45,
                      width: MediaQuery.of(context).size.width * 0.4,
                      text: AppStrings.previous,
                      onPressed: widget.currentStep == 0 ? null : widget.stepProgressController.previousStep,
                    ),
                    CustomButton(
                      height: 45,
                      width: MediaQuery.of(context).size.width * 0.4,
                      text: AppStrings.next,
                      onPressed:  () => widget.stepProgressController.nextStep(),
                    ),
                  ],
                ) : const SizedBox.shrink(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ResourcesForm extends StatefulWidget { 
  final StepProgressController stepProgressController;
  final int currentStep;
  final CreateOfferingFormData formData;
  final bool? viewOnly;
  const ResourcesForm({
    super.key, 
    required this.stepProgressController, 
    required this.currentStep, 
    required this.formData,
    this.viewOnly,
  });

  @override
  State<ResourcesForm> createState() => _ResourcesFormState();
}

class _ResourcesFormState extends State<ResourcesForm> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24,),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: AppColors.primaryTheme.withValues(alpha: 0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  AppStrings.resources,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryTheme,
                  ),
                ),
                widget.viewOnly == true ?
                CustomButton(
                  height: 30,
                  width: 100,
                  text: 'Edit',
                  onPressed: () {
                    widget.stepProgressController.setCurrentStep(3);
                  },
                ) : const SizedBox.shrink(),
              ],
            ),
          ),
        ),
        IgnorePointer(
          ignoring: widget.viewOnly ?? false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10),
                ResourcesListView(formData: widget.formData),
                const SizedBox(height: 10),
                CustomOutlinedButton(
                  height: 35,
                  icon: Icons.add,
                  width: 180,
                  label: AppStrings.addNewResource,
                  onPressed: () async {
                    final result = await AppRoutes.pushScreen<bool>(
                      context,
                      AppRoutes.addingNewResource,
                      arguments: {
                        'formData': widget.formData,
                      },
                    );
          
                    if (result == true) {
                      setState(() {
                        // Refresh the form to show updated resources
                      });
                    }
                  },
                ),
                const SizedBox(height: 24),
                widget.viewOnly == null ?
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomButton(
                      height: 45,
                      width: MediaQuery.of(context).size.width * 0.4,
                      text: AppStrings.previous,
                      onPressed: widget.currentStep == 0 ? null : widget.stepProgressController.previousStep,
                    ),
                    CustomButton(
                      height: 45,
                      width: MediaQuery.of(context).size.width * 0.4,
                      text: AppStrings.next,
                      onPressed:  () => widget.stepProgressController.nextStep(),
                    ),
                  ],
                ): const SizedBox.shrink(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ReviewAndSaveForm extends StatelessWidget { 
  final StepProgressController stepProgressController;
  final int currentStep;
  final CreateOfferingFormData formData;
  const ReviewAndSaveForm({super.key, required this.stepProgressController, required this.currentStep, required this.formData});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Text(
          AppStrings.reviewAndSave,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryTheme,
          ),
        ),
         BasicInfoForm(
          stepProgressController: stepProgressController,
          formData: formData,
          currentStep: currentStep,
          viewOnly: true,
        ),
        DetailsForm(
          stepProgressController: stepProgressController,
          formData: formData,
          currentStep: currentStep,
          viewOnly: true,
        ),
        FAQForm(
          stepProgressController: stepProgressController,
          formData: formData,
          currentStep: currentStep,
          viewOnly: true,
        ),
        ResourcesForm(
          stepProgressController: stepProgressController,
          formData: formData,
          currentStep: currentStep,
          viewOnly: true,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24,),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomButton(
                height: 45,
                width: MediaQuery.of(context).size.width * 0.4,
                text: AppStrings.previous,
                onPressed: currentStep == 0 ? null : stepProgressController.previousStep,
              ),
              BlocBuilder<MerchantOfferBloc, MerchantOfferState>(
                builder: (context, state) {
                  final isLoading = state is MerchantOfferCreating;
                  return CustomButton(
                    height: 45,
                    width: MediaQuery.of(context).size.width * 0.4,
                    text: isLoading ? 'Creating...' : AppStrings.createOfferingBtn,
                    onPressed: isLoading ? null : () {
                      // Handle form submission via API
                      final offeringData = formData.toApiRequest();
                      context.read<MerchantOfferBloc>().add(
                        CreateOfferingEvent(offeringData: offeringData),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
        SizedBox(height: 20,)
      ],
    );
  }
}

class ProductServiceScreen extends StatefulWidget {
  final CreateOfferingFormData formData;

  const ProductServiceScreen({
    super.key,
    required this.formData,
  });

  @override
  State<ProductServiceScreen> createState() => _ProductServiceScreenState();
}

class _ProductServiceScreenState extends State<ProductServiceScreen> {
  void _editService(int index) async {
    final result = await AppRoutes.pushScreen<bool>(
      context,
      AppRoutes.addNewService,
      arguments: {
        'formData': widget.formData,
        'serviceToEdit': widget.formData.services[index],
        'editIndex': index,
      },
    );

    if (result == true) {
      setState(() {
        // Refresh the list
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.formData.services.isEmpty) {
      return SizedBox.shrink();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.formData.services.length,
      itemBuilder: (context, index) {
        final service = widget.formData.services[index];
          return Card(
            color: AppColors.backgroundTheme,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 0,
            child: Column(
              children: [
                // Title Row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(service.serviceName,
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () => _editService(index),
                            child: const Icon(Icons.edit, size: 25, color: AppColors.textPlaceholderTheme),
                          ),
                          const SizedBox(width: 12),
                          GestureDetector(
                            onTap: () => setState(() {
                              widget.formData.services.removeAt(index);
                            }),
                            child: const Icon(Icons.delete_outline, size: 25, color: AppColors.textPlaceholderTheme),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                // SizedBox(height: 8),
            
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16,),
                  child: Column(
                    children: [
                      // SKU
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Item/SKU",
                                style: TextStyle(color: Colors.grey[600], fontSize: 14)),
                          ),
                          Text(service.sku,
                              style: const TextStyle( fontSize: 14,fontWeight: FontWeight.bold)),
                        ],
                      ),
                      // SizedBox(height: 8),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                      // Type Badge
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Type:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.green,
                              ),
                              borderRadius: BorderRadius.circular(4),
                              color: Colors.green.withValues(alpha: 0.1),
                            ),
                            child: Text(
                              service.type ?? '',
                              style: const TextStyle(
                                color:Colors.green,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                      // Prices
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Min Price:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text('\$${service.minSellingPrice}',
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Max Price:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text('\$${service.maxSellingPrice}',
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      SizedBox(height: 10),
                    ]
                  ),
                )
              ],
            ),
          );
        },
      );
  }
}

class AddCompetitorScreen extends StatefulWidget {
  final CreateOfferingFormData formData;

  const AddCompetitorScreen({
    super.key,
    required this.formData,
  });

  @override
  State<AddCompetitorScreen> createState() => _AddCompetitorScreenState();
}

class _AddCompetitorScreenState extends State<AddCompetitorScreen> {
  void _editService(int index) async {
    final result = await AppRoutes.pushScreen<bool>(
      context,
      AppRoutes.addCompetitor,
      arguments: {
        'formData': widget.formData,
        'competitorToEdit': widget.formData.competitors[index],
        'editIndex': index,
      },
    );

    if (result == true) {
      setState(() {
        // Refresh the list
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.formData.competitors.isEmpty) {
      return SizedBox.shrink();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.formData.competitors.length,
      itemBuilder: (context, index) {
        final competitor = widget.formData.competitors[index];
          return Card(
            color: AppColors.backgroundTheme,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 0,
            child: Column(
              children: [
                // Title Row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Action',
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () => _editService(index),
                            child: const Icon(Icons.edit, size: 25, color: AppColors.textPlaceholderTheme),
                          ),
                          const SizedBox(width: 12),
                          GestureDetector(
                            onTap: () => setState(() {
                              widget.formData.competitors.removeAt(index);
                            }),
                            child: const Icon(Icons.delete_outline, size: 25, color: AppColors.textPlaceholderTheme),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                // SizedBox(height: 8),
            
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16,),
                  child: Column(
                    children: [
                      // SKU
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Competitor",
                                style: TextStyle(color: Colors.grey[600], fontSize: 14)),
                          ),
                          Text(competitor.competitorName,
                              style: const TextStyle( fontSize: 14,fontWeight: FontWeight.bold)),
                        ],
                      ),
                      // SizedBox(height: 8),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                      // Type Badge
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Notes:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          SizedBox(
                            width: MediaQuery.of(context).size.width/2,
                            child: Text(competitor.notes,
                            overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                      // Prices
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Website:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text(competitor.link,
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                       SizedBox(height: 10),
                    ]
                  ),
                )
              ],
            ),
          );
        },
      );
  }
}
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/error/failures.dart';
import '../repositories/auth_repository.dart';
import '../../data/models/register_response.dart';

class RegisterUseCase {
  final AuthRepository repository;

  RegisterUseCase(this.repository);

  Future<Either<Failure, RegisterResponse>> call(RegisterParams params) async {
    return await repository.register(
      firstName: params.firstName,
      lastName: params.lastName,
      email: params.email,
      password: params.password,
      type: params.type,
      companyName: params.companyName,
      companyLegalName: params.companyLegalName,
      crNumber: params.crNumber,
      investedCapital: params.investedCapital,
      investedCapitalUnit: params.investedCapitalUnit,
      companySize: params.companySize,
      licenseDocument: params.licenseDocument,
      crDocument: params.crDocument,
    );
  }
}

class RegisterParams extends Equatable {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String type;
  // Merchant fields (optional)
  final String? companyName;
  final String? companyLegalName;
  final String? crNumber;
  final int? investedCapital;
  final String? investedCapitalUnit;
  final String? companySize;
  final String? licenseDocument;
  final String? crDocument;

  const RegisterParams({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    required this.type,
    this.companyName,
    this.companyLegalName,
    this.crNumber,
    this.investedCapital,
    this.investedCapitalUnit,
    this.companySize,
    this.licenseDocument,
    this.crDocument,
  });

  @override
  List<Object?> get props => [
    firstName, lastName, email, password, type,
    companyLegalName, crNumber, investedCapital, investedCapitalUnit, companySize, licenseDocument, crDocument
  ];
}

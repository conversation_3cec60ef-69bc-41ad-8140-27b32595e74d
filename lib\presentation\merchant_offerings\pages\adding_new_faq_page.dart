import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class AddingNewFaqPage extends StatefulWidget {
  final CreateOfferingFormData formData;

  const AddingNewFaqPage({
    super.key,
    required this.formData,
  });

  @override
  State<AddingNewFaqPage> createState() => _AddingNewFaqPageState();
}

class _AddingNewFaqPageState extends State<AddingNewFaqPage> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _answerController = TextEditingController();
  final _questionUrlController = TextEditingController();

  void _saveFaq() {
    if (_formKey.currentState?.validate() ?? false) {
      final faq = FAQFormData(
        question: _questionController.text.trim(),
        answer: _answerController.text.trim(),
        questionUrl: _questionUrlController.text.trim(),
      );
       final modifiableList = [...widget.formData.faqs];
      modifiableList.add(faq);
      widget.formData.faqs = modifiableList;

      Navigator.pop(context, true); // Return true to indicate success
    }
  }

  void _cancel() {
    Navigator.pop(context, false);
  }

  @override
  void dispose() {
    _questionController.dispose();
    _answerController.dispose();
    _questionUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        foregroundColor: Colors.black,
        title: const Text(AppStrings.addingNewFaq),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField(
                controller: _questionController,
                hintText: AppStrings.enterQuestion,
                validator: (value) => Validators.validateRequired(value, AppStrings.frequentlyAskedQuestion),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _answerController,
                hintText: AppStrings.enterAnswer,
                maxLines: 4,
                maxLength: 500,
                validator: (value) => Validators.validateRequired(value, AppStrings.enterAnswer),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _questionUrlController,
                hintText: AppStrings.enterQuestionUrl,
                keyboardType: TextInputType.url,
                // No validation since it's optional
              ),
              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Cancel',
                      onPressed: _cancel,
                      backgroundColor: Colors.grey.shade300,
                      textColor: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: 'Save',
                      onPressed: _saveFaq,
                      backgroundColor: AppColors.primaryTheme,
                      textColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/merchant_offering_model.dart';
import '../repositories/merchant_offering_repository.dart';

class GetMerchantOfferingsUseCase {
  final MerchantOfferingRepository repository;

  GetMerchantOfferingsUseCase(this.repository);

  Future<Either<Failure, MerchantOfferingResponse>> call({
    int page = 1,
    int size = 9,
  }) async {
    return await repository.getMerchantOfferings(page: page, size: size);
  }
}

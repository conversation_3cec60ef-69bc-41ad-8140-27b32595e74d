import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/img_string.dart';
import 'package:wesell/core/di/injection_container.dart';
import 'package:wesell/presentation/marchant_dashboard/blocs/acticity/activity_bloc.dart';
import 'package:wesell/presentation/marchant_dashboard/blocs/marchant_counter/merchant_counter_bloc.dart';
import 'package:wesell/presentation/marchant_dashboard/pages/home_page.dart';
import 'package:wesell/presentation/merchant_offerings/pages/merchant_offering_page.dart';
import 'package:wesell/presentation/merchant_offerings/blocs/merchant_offer_bloc.dart';
import 'package:wesell/presentation/settings/pages/settings_page.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_bloc.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_event.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_state.dart';
import 'package:wesell/presentation/widgets/custom_svg_widget.dart';


class TabsPage extends StatelessWidget {
  const TabsPage({super.key});

  static final List<Widget> _screens = [
    MultiBlocProvider(
      providers: [
        BlocProvider<MerchantStatsBloc>(
          create: (context) => serviceLocator<MerchantStatsBloc>(),
        ),
        BlocProvider<ActivityBloc>(
          create: (context) => serviceLocator<ActivityBloc>(),
        ),
      ],
      child: const HomePage(),
    ),
    BlocProvider(
      create: (context) => serviceLocator<MerchantOfferBloc>(),
      child: const MerchantOfferingPage(),
    ),
    const Center(child: Text('jobs')),
    const Center(child: Text('chat')),
    const SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TabsBloc, TabsState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.whiteTheme,
          body: _screens[state.selectedIndex],
          bottomNavigationBar: Theme(
            data: ThemeData(
              splashColor: Colors.transparent,
            ),
            child: BottomNavigationBar(
              backgroundColor: AppColors.whiteTheme,
              currentIndex: state.selectedIndex,
              selectedItemColor: Colors.green,
              unselectedItemColor: Colors.grey,
              type: BottomNavigationBarType.fixed,
              showSelectedLabels: false, 
              showUnselectedLabels: false,
              onTap: (index) =>
                  context.read<TabsBloc>().add(TabChanged(index)),
              items: [
                BottomNavigationBarItem(
                  icon: SvgIcon(
                    assetPath: ImageStrings.home,
                    color: state.selectedIndex == 0 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                  ),
                  label: '',
                ),
                BottomNavigationBarItem(
                  icon: SvgIcon(
                    assetPath: ImageStrings.seller,
                    color: state.selectedIndex == 1 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                  ),
                  label: '',
                ),
                BottomNavigationBarItem(
                  icon: SvgIcon(
                    assetPath: ImageStrings.jobs,
                    color: state.selectedIndex == 2 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                  ),
                  label: '',
                ),
                BottomNavigationBarItem(
                  icon: SvgIcon(
                    assetPath: ImageStrings.chat,
                    color: state.selectedIndex == 3 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                  ),
                  label: '',
                ),
                BottomNavigationBarItem(
                  icon: SvgIcon(
                    assetPath: ImageStrings.settings,
                    color: state.selectedIndex == 4 ? AppColors.primaryTheme : AppColors.iconLightTheme,
                  ),
                  label: '',
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

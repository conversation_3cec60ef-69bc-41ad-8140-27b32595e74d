import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/services/helper_services.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';
import 'package:wesell/presentation/widgets/custom_shimmer_animation.dart';

class  ResourcesListView extends StatefulWidget {
  final CreateOfferingFormData formData;

  const  ResourcesListView({
    super.key,
    required this.formData,
  });

  @override
  State<ResourcesListView> createState() => _ResourcesListViewState();
}

class _ResourcesListViewState extends State<ResourcesListView> {

  @override
  Widget build(BuildContext context) {
    if (widget.formData.resources.isEmpty) {
      return SizedBox.shrink();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.formData.resources.length,
      itemBuilder: (context, index) {
        final resource = widget.formData.resources[index];
          return Card(
            color: AppColors.backgroundTheme,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 0,
            child: Column(
              children: [
                // Title Row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      avatar(widget.formData.resources[index].resourceFileId),
                      GestureDetector(
                        onTap: () => setState(() {
                          widget.formData.resources.removeAt(index);
                        }),
                        child: const Icon(Icons.delete_outline, size: 25, color: AppColors.textPlaceholderTheme),
                      ),
                    ],
                  ),
                ),
                Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16,),
                  child: Column(
                    children: [
                      // SKU
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("title:",
                                style: TextStyle(color: Colors.grey[600], fontSize: 14)),
                          ),
                          Text(resource.resourceTitle,
                              style: const TextStyle( fontSize: 14,fontWeight: FontWeight.bold)),
                        ],
                      ),
                      // SizedBox(height: 8),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
            
                      // Type Badge
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Description:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          SizedBox(
                            width: MediaQuery.of(context).size.width/2,
                            child: Text(resource.resourceDescription,
                            overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                      // Prices
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Category:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text(resource.category,
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                      // Prices
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Link:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text(resource.resourceVideoLink,
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      SizedBox(height: 10),
                    ]
                  ),
                )
              ],
            ),
          );
        },
      );
  }

   Widget avatar(id){
    return ClipOval(
      child: Image.network(
        AppConstants.mediaBaseUrl +id,
        width: 28,
        height: 28,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 28,
            height: 28,
              color: AppColors.iconLightTheme,
              alignment: Alignment.center,
              child: Text(
                HelperServices.getInitialLetter(id),
                style: TextStyle(color: Colors.white),
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Shimmer(
              child: Container(
                width: 28,
                height: 28,
                color: AppColors.iconLightTheme,
              ),
            ); // Loading state
          },
      ),
    );
  }
}
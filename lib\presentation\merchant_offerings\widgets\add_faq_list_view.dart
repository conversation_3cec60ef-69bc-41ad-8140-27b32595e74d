import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class AddFaqListView extends StatefulWidget {
  final CreateOfferingFormData formData;

  const AddFaqListView({
    super.key,
    required this.formData,
  });

  @override
  State<AddFaqListView> createState() => _AddFaqListViewState();
}

class _AddFaqListViewState extends State<AddFaqListView> {

  @override
  Widget build(BuildContext context) {
    if (widget.formData.faqs.isEmpty) {
      return SizedBox.shrink();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 0),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.formData.faqs.length,
      itemBuilder: (context, index) {
         final faq = widget.formData.faqs[index];
          return Card(
            color: AppColors.backgroundTheme,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 0,
            child: Column(
              children: [
                SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16,),
                  child: Column(
                    children: [
                      // SKU
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Question:",
                                style: TextStyle(color: Colors.grey[600], fontSize: 14)),
                          ),
                          Flexible(
                            child: Container(
                              alignment: Alignment.centerLeft,
                              //width: MediaQuery.of(context).size.width/2.2,
                              child: Text(faq.question,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                                  style: const TextStyle( fontSize: 14,fontWeight: FontWeight.bold)),
                            ),
                          ),
                          SizedBox(
                            width: 20,
                            child: GestureDetector(
                            onTap: () => setState(() {
                              widget.formData.faqs.removeAt(index);
                            }),
                            child: const Icon(Icons.delete_outline, size: 25, color: AppColors.textPlaceholderTheme),
                            ),
                          ),
                        ],
                      ),
                      // SizedBox(height: 8),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),

                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Answer:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          SizedBox(
                            width: MediaQuery.of(context).size.width/2,
                            child: Text(faq.answer,
                            overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                      const Divider(color: AppColors.iconLightTheme,thickness: 0.5,),
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width/4,
                            child: Text("Question URL:",
                                style: TextStyle(color: Colors.grey[700])),
                          ),
                          Text(faq.questionUrl,
                              style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                       SizedBox(height: 10),
                    ]
                  ),
                )
              ],
            ),
          );
        },
      );
  }
}

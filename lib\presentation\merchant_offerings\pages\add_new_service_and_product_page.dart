import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class AddNewServiceAndProductPage extends StatefulWidget {
  final CreateOfferingFormData formData;
  final ServiceFormData? serviceToEdit;
  final int? editIndex;

  const AddNewServiceAndProductPage({
    super.key,
    required this.formData,
    this.serviceToEdit,
    this.editIndex,
  });

  @override
  State<AddNewServiceAndProductPage> createState() => _AddNewServiceAndProductPageState();
}

class _AddNewServiceAndProductPageState extends State<AddNewServiceAndProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _serviceNameController = TextEditingController();
  final _serviceDescriptionController = TextEditingController();
  final _minSellingPriceController = TextEditingController();
  final _maxSellingPriceController = TextEditingController();
  final _skuController = TextEditingController();
  final _costController = TextEditingController();
  
  String? _selectedType;
  bool _needsShipping = false;
  String? _serviceImageId;
  
  final List<String> _typeOptions = ['Services', 'Product'];

  @override
  void initState() {
    super.initState();
    if (widget.serviceToEdit != null) {
      _initializeEditMode();
    }
  }

  void _initializeEditMode() {
    final service = widget.serviceToEdit!;
    _serviceNameController.text = service.serviceName;
    _serviceDescriptionController.text = service.serviceDescription;
    _minSellingPriceController.text = service.minSellingPrice.toString();
    _maxSellingPriceController.text = service.maxSellingPrice.toString();
    _skuController.text = service.sku;
    _costController.text = service.cost.toString();
    _selectedType = service.type!;
    _needsShipping = service.needsShipping;
    _serviceImageId = service.serviceImageId;
  }

  String? _validateMinSellingPrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Minimum Selling Price is required';
    }
    final minValue = int.tryParse(value);
    if (minValue == null || minValue <= 0) {
      return 'Minimum Selling Price must be a positive number';
    }
    final maxValue = int.tryParse(_maxSellingPriceController.text);
    if (maxValue != null && minValue > maxValue) {
      return 'Minimum Selling Price cannot be greater than Maximum Selling Price';
    }
    return null;
  }

  String? _validateMaxSellingPrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Maximum Selling Price is required';
    }
    final maxValue = int.tryParse(value);
    if (maxValue == null || maxValue <= 0) {
      return 'Maximum Selling Price must be a positive number';
    }
    final minValue = int.tryParse(_minSellingPriceController.text);
    if (minValue != null && maxValue < minValue) {
      return 'Maximum Selling Price cannot be less than Minimum Selling Price';
    }
    return null;
  }

  String? _validateSku(String? value) {
    if (value == null || value.isEmpty) {
      return 'Item/SKU cannot be empty';
    }
    return null;
  }

  String? _validateCost(String? value) {
    if (value == null || value.isEmpty) {
      return 'Cost cannot be empty';
    }
    final cost = double.tryParse(value);
    if (cost == null || cost <= 0) {
      return 'Cost must be a positive number';
    }
    return null;
  }

  String? _validateDescription(String? value) {
    if (value == null || value.isEmpty) {
      return 'Product or service description is required';
    }
    if (value.length > 500) {
      return 'Description must be 500 characters or less';
    }
    return null;
  }

  void _saveService() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_serviceImageId == null || _serviceImageId!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a product/service image'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final service = ServiceFormData(
        serviceName: _serviceNameController.text.trim(),
        serviceDescription: _serviceDescriptionController.text.trim(),
        minSellingPrice: int.parse(_minSellingPriceController.text),
        maxSellingPrice: int.parse(_maxSellingPriceController.text),
        sku: _skuController.text.trim(),
        type: _selectedType,
        cost: double.parse(_costController.text),
        needsShipping: _needsShipping,
        serviceImageId: _serviceImageId,
      );

      if (widget.editIndex != null) {
        // Edit existing service
        final modifiableList = [...widget.formData.services];
        modifiableList[widget.editIndex!] = service;
        widget.formData.services = modifiableList;
      } else {
        // Add new service
        final modifiableList = [...widget.formData.services]; // create a mutable copy
        modifiableList.add(service);
        widget.formData.services = modifiableList; // replace with updated list
      }

      Navigator.pop(context, true); // Return true to indicate success
    }
  }

  void _cancel() {
    Navigator.pop(context, false);
  }

  @override
  void dispose() {
    _serviceNameController.dispose();
    _serviceDescriptionController.dispose();
    _minSellingPriceController.dispose();
    _maxSellingPriceController.dispose();
    _skuController.dispose();
    _costController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        foregroundColor: Colors.black,
        title: const Text('Add a new product/service'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField(
                controller: _serviceNameController,
                hintText: 'Enter product/service name',
                validator: (value) => Validators.validateRequired(value, 'Product/Service Name'),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _serviceDescriptionController,
                hintText: 'Enter your product or service description',
                maxLines: 3,
                maxLength: 500,
                validator: _validateDescription,
              ),
              const Text(
                'Selling Price (Min & Max)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              // Selling Price Section
              Row(children: [
                Expanded(
                  child: CustomTextField(
                    controller: _minSellingPriceController,
                    hintText: 'Enter min price',
                    keyboardType: TextInputType.number,
                    validator: _validateMinSellingPrice,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _maxSellingPriceController,
                    hintText: 'Enter max price',
                    keyboardType: TextInputType.number,
                    validator: _validateMaxSellingPrice,
                  ),
                ),
              ],
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _skuController,
                hintText: 'Enter item/SKU',
                validator: _validateSku,
              ),
              const SizedBox(height: 16),
              CustomDropdownFormField<String>(
                value: _selectedType,
                items: _typeOptions,
                hintText: 'Select type',
                itemLabel: (item) => item,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value ?? 'Services';
                  });
                },
                validator: (value) => value == null ? 'Please select a type' : null,
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _costController,
                hintText: 'Enter cost',
                keyboardType: TextInputType.number,
                validator: _validateCost,
              ),
              // Needs Shipping Checkbox
              Row(
                children: [
                  Checkbox(
                    value: _needsShipping,
                    onChanged: (value) {
                      setState(() {
                        _needsShipping = value ?? false;
                      });
                    },
                    activeColor: AppColors.primaryTheme,
                  ),
                  const Text(
                    'Needs shipping',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Upload Image
              const Text(
                'Upload this product/service image',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              FileUploadWidget(
                label: 'Upload product/service image',
                uploadId: 'service_image',
                onFileUploaded: (fileId){
                  setState(() {
                    _serviceImageId = fileId == '' ? null : fileId;
                  });
                },
                currentFileId: _serviceImageId,
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Cancel',
                      onPressed: _cancel,
                      backgroundColor: Colors.grey.shade300,
                      textColor: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: 'Save',
                      onPressed: _saveService,
                      backgroundColor: AppColors.primaryTheme,
                      textColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

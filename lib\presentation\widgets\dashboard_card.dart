import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/presentation/widgets/custom_svg_widget.dart';

class DashboardStatCard extends StatelessWidget {
  final String? value;
  final String label;
  final String subtitle;
  final String icon;
  final Color iconBgColor;

  const DashboardStatCard({
    super.key,
    this.value,
    required this.label,
    required this.subtitle,
    required this.icon,
    required this.iconBgColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: AppColors.whiteTheme,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 28,
                  width: 28,
                  decoration: BoxDecoration(
                    color: iconBgColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: Center(
                    child: SvgIcon(assetPath: icon),
                  ),
                ),
                const Row(
                  children: [
                    Icon(Icons.arrow_upward, color: AppColors.primaryTheme, size: 14),
                    Text(
                      '50.8%',
                      style: TextStyle(color: AppColors.primaryTheme, fontSize: 15),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value ?? '0',
              style: const TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
            ),
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w600,fontSize: 14),
            ),
             const SizedBox(height: 8),
            Text(
              subtitle,
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

class ActivityDoer {
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? type;
  final String? userId;
  final String? avatar;

  ActivityDoer({
    this.firstName,
    this.lastName,
    this.email,
    this.type,
    this.userId,
    this.avatar,
  });

  factory ActivityDoer.fromJson(Map<String, dynamic> json) {
    return ActivityDoer(
      firstName: json['firstName'],
      lastName: json['lastName'],
      email: json['email'],
      type: json['type'],
      userId: json['userId'],
      avatar: json['avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'type': type,
      'userId': userId,
      'avatar': avatar,
    };
  }
}

class ActivityModel {
  final ActivityDoer? doer;
  final List<String>? observers;
  final String? entityId;
  final String? entityName;
  final String? action;
  final String? activityId;
  final String? createdAt;
  final String? updatedAt;

  ActivityModel({
    this.doer,
    this.observers,
    this.entityId,
    this.entityName,
    this.action,
    this.activityId,
    this.createdAt,
    this.updatedAt,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      doer: json['doer'] != null ? ActivityDoer.fromJson(json['doer']) : null,
      observers: json['observers'] != null 
          ? List<String>.from(json['observers']) 
          : null,
      entityId: json['entityId'],
      entityName: json['entityName'],
      action: json['action'],
      activityId: json['activityId'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'doer': doer?.toJson(),
      'observers': observers,
      'entityId': entityId,
      'entityName': entityName,
      'action': action,
      'activityId': activityId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}

class ActivitiesResponse {
  final List<ActivityModel>? rows;
  final int? total;
  final int? size;
  final int? page;

  ActivitiesResponse({
    this.rows,
    this.total,
    this.size,
    this.page,
  });

  factory ActivitiesResponse.fromJson(Map<String, dynamic> json) {
    return ActivitiesResponse(
      rows: json['rows'] != null
          ? (json['rows'] as List)
              .map((item) => ActivityModel.fromJson(item))
              .toList()
          : null,
      total: json['total'],
      size: json['size'],
      page: json['page'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rows': rows?.map((item) => item.toJson()).toList(),
      'total': total,
      'size': size,
      'page': page,
    };
  }
}

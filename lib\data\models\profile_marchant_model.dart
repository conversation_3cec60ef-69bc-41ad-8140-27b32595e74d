class ProfileMarchantModel {
  final String? id;
  final String? companyName;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? type;
  final String? userId;
  final String? createdAt;
  final String? updatedAt;
  final int? version;
  final ProfileAttributes? attributes;
  final CompanyModel? company;

  ProfileMarchantModel({
    this.id,
    this.companyName,
    this.firstName,
    this.lastName,
    this.email,
    this.type,
    this.userId,
    this.createdAt,
    this.updatedAt,
    this.version,
    this.attributes,
    this.company,
  });

  factory ProfileMarchantModel.fromJson(Map<String, dynamic> json) {
    return ProfileMarchantModel(
      id: json['_id'] as String?,
      companyName: json['companyName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      email: json['email'] as String?,
      type: json['type'] as String?,
      userId: json['userId'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      version: json['__v'] as int?,
      attributes: json['attributes'] != null
          ? ProfileAttributes.fromJson(json['attributes'])
          : null,
      company: json['company'] != null
          ? CompanyModel.fromJson(json['company'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'companyName': companyName,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'type': type,
      'userId': userId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      '__v': version,
      'attributes': attributes?.toJson(),
      'company': company?.toJson(),
    };
  }
}

class ProfileAttributes {
  final int? profileProgress;
  final CompanyDetails? companyDetails;
  final List<String>? savedSellers;
  final CompanyLocations? companyLocations;
  final List<String>? companyIndustries;
  final CompanyDigitalInformations? companyDigitalInformations;
  final bool? hasInvited;
  final BankDetails? bankDetails;

  ProfileAttributes({
    this.profileProgress,
    this.companyDetails,
    this.savedSellers,
    this.companyLocations,
    this.companyIndustries,
    this.companyDigitalInformations,
    this.hasInvited,
    this.bankDetails,
  });

  factory ProfileAttributes.fromJson(Map<String, dynamic> json) {
    return ProfileAttributes(
      profileProgress: json['profileProgress'] as int?,
      companyDetails: (json['companyDetails'] != null && json['companyDetails'] is Map<String, dynamic>)
          ? CompanyDetails.fromJson(json['companyDetails'] as Map<String, dynamic>)
          : null,
      savedSellers: json['savedSellers'] != null
          ? List<String>.from(json['savedSellers'])
          : null,
      companyLocations: json['companyLocations'] != null && json['companyLocations'] is Map<String, dynamic>
          ? CompanyLocations.fromJson(json['companyLocations'] as Map<String, dynamic>)
          : null,
      companyIndustries: json['companyIndustries'] != null
          ? List<String>.from(json['companyIndustries'])
          : null,
      companyDigitalInformations: json['companyDigitalInformations'] != null && json['companyDigitalInformations'] is Map<String, dynamic>
          ? CompanyDigitalInformations.fromJson(json['companyDigitalInformations'] as Map<String, dynamic>)
          : null,
      hasInvited: json['hasInvited'] as bool?,
      bankDetails: json['bankDetails'] != null && json['bankDetails'] is Map<String, dynamic>
          ? BankDetails.fromJson(json['bankDetails'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'profileProgress': profileProgress,
      'companyDetails': companyDetails?.toJson(),
      'savedSellers': savedSellers,
      'companyLocations': companyLocations?.toJson(),
      'companyIndustries': companyIndustries,
      'companyDigitalInformations': companyDigitalInformations?.toJson(),
      'hasInvited': hasInvited,
      'bankDetails': bankDetails?.toJson(),
    };
  }
}

class CompanyDetails {
  final String? logo;
  final String? description;
  final String? detailedDescription;
  final String? companyName;

  CompanyDetails({
    this.logo,
    this.description,
    this.detailedDescription,
    this.companyName,
  });

  factory CompanyDetails.fromJson(Map<String, dynamic> json) {
    return CompanyDetails(
      logo: json['logo'] as String?,
      description: json['description'] as String?,
      detailedDescription: json['detailedDescription'] as String?,
      companyName: json['companyName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logo': logo,
      'description': description,
      'detailedDescription': detailedDescription,
      'companyName': companyName,
    };
  }
}

class CompanyLocations {
  final String? country;
  final String? city;
  final String? address;
  final String? poBox;

  CompanyLocations({
    this.country,
    this.city,
    this.address,
    this.poBox,
  });

  factory CompanyLocations.fromJson(Map<String, dynamic> json) {
    return CompanyLocations(
      country: json['country'] as String?,
      city: json['city'] as String?,
      address: json['address'] as String?,
      poBox: json['poBox'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'city': city,
      'address': address,
      'poBox': poBox,
    };
  }
}

class CompanyDigitalInformations {
  final String? website;
  final String? facebook;
  final String? twitter;
  final String? linkedin;
  final String? youtube;
  final String? tiktok;
  final String? snapchat;

  CompanyDigitalInformations({
    this.website,
    this.facebook,
    this.twitter,
    this.linkedin,
    this.youtube,
    this.tiktok,
    this.snapchat,
  });

  factory CompanyDigitalInformations.fromJson(Map<String, dynamic> json) {
    return CompanyDigitalInformations(
      website: json['website'] as String?,
      facebook: json['facebook'] as String?,
      twitter: json['twitter'] as String?,
      linkedin: json['linkedin'] as String?,
      youtube: json['youtube'] as String?,
      tiktok: json['tiktok'] as String?,
      snapchat: json['snapchat'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'website': website,
      'facebook': facebook,
      'twitter': twitter,
      'linkedin': linkedin,
      'youtube': youtube,
      'tiktok': tiktok,
      'snapchat': snapchat,
    };
  }
}

class BankDetails {
  final String? iban;
  final String? accountName;
  final String? bankName;
  final String? ibanCertificate;

  BankDetails({
    this.iban,
    this.accountName,
    this.bankName,
    this.ibanCertificate,
  });

  factory BankDetails.fromJson(Map<String, dynamic> json) {
    return BankDetails(
      iban: json['iban'] as String?,
      accountName: json['accountName'] as String?,
      bankName: json['bankName'] as String?,
      ibanCertificate: json['ibanCertificate'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iban': iban,
      'accountName': accountName,
      'bankName': bankName,
      'ibanCertificate': ibanCertificate,
    };
  }
}

class CompanyModel {
  final String? id;
  final String? crNumber;
  final String? userId;
  final String? companySize;
  final String? companyLegalName;
  final int? investedCapital;
  final String? investedCapitalUnit;
  final String? crDocument;
  final String? licenseDocument;
  final String? createdAt;
  final String? updatedAt;
  final int? version;

  CompanyModel({
    this.id,
    this.crNumber,
    this.userId,
    this.companySize,
    this.companyLegalName,
    this.investedCapital,
    this.investedCapitalUnit,
    this.crDocument,
    this.licenseDocument,
    this.createdAt,
    this.updatedAt,
    this.version,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      id: json['_id'] as String?,
      crNumber: json['crNumber'] as String?,
      userId: json['userId'] as String?,
      companySize: json['companySize'] as String?,
      companyLegalName: json['companyLegalName'] as String?,
      investedCapital: json['investedCapital'] as int?,
      investedCapitalUnit: json['investedCapitalUnit'] as String?,
      crDocument: json['crDocument'] as String?,
      licenseDocument: json['licenseDocument'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      version: json['__v'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'crNumber': crNumber,
      'userId': userId,
      'companySize': companySize,
      'companyLegalName': companyLegalName,
      'investedCapital': investedCapital,
      'investedCapitalUnit': investedCapitalUnit,
      'crDocument': crDocument,
      'licenseDocument': licenseDocument,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      '__v': version,
    };
  }
}

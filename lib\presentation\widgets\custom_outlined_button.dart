import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';

class CustomOutlinedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String label;
  final IconData? icon;
  final Color? iconColor;
  final Color? textColor;
  final Color? borderColor;
  final double? width;
  final double? height;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? fontSize;
  final FontWeight? fontWeight;

  const CustomOutlinedButton({
    super.key,
    this.onPressed,
    required this.label,
    this.icon,
    this.iconColor,
    this.textColor,
    this.borderColor,
    this.width,
    this.height,
    this.borderRadius,
    this.padding,
    this.fontSize,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: icon != null 
          ? Icon(icon, color: iconColor ?? AppColors.primaryTheme,)
          : const SizedBox.shrink(),
      label: Text(
        label,
        style: TextStyle(
          color: textColor ?? AppColors.primaryTheme,
          fontSize: fontSize ?? 14,
          fontWeight: fontWeight ?? FontWeight.w500,
        ),
      ),
      style: OutlinedButton.styleFrom(
        fixedSize: width != null || height != null 
            ? Size(width ?? 0, height ?? 0)
            : null,
        side: BorderSide(color: borderColor ?? AppColors.primaryTheme),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
        ),
        padding: padding ?? EdgeInsets.symmetric(vertical: 10),
      ),
    );
  }
}

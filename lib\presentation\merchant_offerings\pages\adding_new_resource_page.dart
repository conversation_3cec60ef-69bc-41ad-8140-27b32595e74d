import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class AddingNewResourcePage extends StatefulWidget {
  final CreateOfferingFormData formData;

  const AddingNewResourcePage({
    super.key,
    required this.formData,
  });

  @override
  State<AddingNewResourcePage> createState() => _AddingNewResourcePageState();
}

class _AddingNewResourcePageState extends State<AddingNewResourcePage> {
  final _formKey = GlobalKey<FormState>();
  final _resourceTitleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _videoLinkController = TextEditingController();
  String? _uploadedFileId;

  void _saveResource() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_uploadedFileId == null || _uploadedFileId!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a resource file'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final resource = ResourceFormData(
        resourceTitle: _resourceTitleController.text.trim(),
        resourceDescription: _descriptionController.text.trim(),
        resourceFileId: _uploadedFileId!,
        category: _categoryController.text.trim(),
        resourceVideoLink: _videoLinkController.text.trim(),
      );
       final modifiableList = [...widget.formData.resources];
      modifiableList.add(resource);
      widget.formData.resources = modifiableList;
      
      Navigator.pop(context, true);
    }
  }

  void _cancel() {
    Navigator.pop(context, false);
  }

  @override
  void dispose() {
    _resourceTitleController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _videoLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        foregroundColor: Colors.black,
        title: const Text(AppStrings.addingNewResource),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Resource Title
              CustomTextField(
                controller: _resourceTitleController,
                hintText: AppStrings.enterResourceTitle,
                validator: (value)=> Validators.validateRequired(value, AppStrings.resourceTitle),
              ),
              const SizedBox(height: 16),
              // Description
              CustomTextField(
                controller: _descriptionController,
                hintText: AppStrings.enterDescription,
                maxLines: 3,
                validator:(value)=> Validators.validateRequired(value, AppStrings.description),
              ),
              const SizedBox(height: 16),
              // Resource File
              FileUploadWidget(
                allowedExtensions:['jpg', 'jpeg', 'png'],
                label: AppStrings.uploadResourceFile,
                uploadId: 'resource_file',
                onFileUploaded: (fileId) {
                  setState(() {
                    _uploadedFileId = fileId == '' ? null : fileId;
                  });
                },
                currentFileId: _uploadedFileId,
              ),
              const SizedBox(height: 16),
              // Category
              CustomTextField(
                controller: _categoryController,
                hintText: AppStrings.enterCategory,
                validator: (value)=> Validators.validateRequired(value, AppStrings.category),
              ),
              const SizedBox(height: 16),
              // Resource Video Link
              CustomTextField(
                controller: _videoLinkController,
                hintText: AppStrings.enterVideoLink,
                keyboardType: TextInputType.url,
                validator: (value) => Validators.validateUrl(value),
              ),
              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Cancel',
                      onPressed: _cancel,
                      backgroundColor: Colors.grey.shade300,
                      textColor: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: 'Save',
                      onPressed: _saveResource,
                      backgroundColor: AppColors.primaryTheme,
                      textColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

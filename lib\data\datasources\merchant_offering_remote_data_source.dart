import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../../domain/usecases/create_offering_usecase.dart';
import '../models/merchant_offering_model.dart';

abstract class MerchantOfferingRemoteDataSource {
  Future<MerchantOfferingResponse> getMerchantOfferings({
    required int page,
    required int size,
  });

  Future<CreateOfferingResponse> createOffering(Map<String, dynamic> offeringData);
}

class MerchantOfferingRemoteDataSourceImpl implements MerchantOfferingRemoteDataSource {
  final DioClient dioClient;

  MerchantOfferingRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<MerchantOfferingResponse> getMerchantOfferings({
    required int page,
    required int size,
  }) async {
    final response = await dioClient.get(
      AppConstants.merchantOfferingsEndpoint,
      queryParameters: {
        'page': page,
        'size': size,
      },
    );
    return MerchantOfferingResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<CreateOfferingResponse> createOffering(Map<String, dynamic> offeringData) async {
    final response = await dioClient.post(
      AppConstants.merchantOfferingsEndpoint,
      data: offeringData,
    );
    return CreateOfferingResponse.fromJson(response.data as Map<String, dynamic>);
  }
}

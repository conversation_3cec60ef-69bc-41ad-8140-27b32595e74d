import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';

class CustomDropdownFormField<T> extends StatelessWidget {
  final T? value;
  final List<T> items;
  final String hintText;
  final String? Function(T?)? validator;
  final void Function(T?)? onChanged;
  final String Function(T) itemLabel;
  final bool? disabled;
  final double? borderRadius;
  final EdgeInsetsGeometry? contentPadding;
  final Color? focusedBorderColor;

  const CustomDropdownFormField({
    super.key,
    required this.value,
    required this.items,
    required this.hintText,
    required this.itemLabel,
    this.validator,
    this.onChanged,
    this.disabled,
    this.borderRadius,
    this.contentPadding,
    this.focusedBorderColor,
  });

  @override
  Widget build(BuildContext context) {
    BorderRadius radius = BorderRadius.circular(borderRadius ?? 12);
    return DropdownButtonFormField<T>(
      dropdownColor: AppColors.whiteTheme,
      value: value,
      items: items
      .map((item) => DropdownMenuItem<T>(
            value: item,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.7,
              child: Text(itemLabel(item),style: TextStyle(color:focusedBorderColor!=null ?AppColors.textPlaceholderTheme: AppColors.blackTextTheme),)),
          ))
      .toList(),
      decoration: InputDecoration(
        hintText: hintText,
        border: OutlineInputBorder(
          borderRadius: radius
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: AppColors.borderTheme,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color:focusedBorderColor ?? AppColors.primaryTheme,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: const BorderSide(
            color: AppColors.errorTheme,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: const BorderSide(
            color: AppColors.errorTheme,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: AppColors.whiteTheme,
        contentPadding: contentPadding ?? const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
      validator: validator,
      onChanged: disabled == true ? null : onChanged,
    );
  }
}

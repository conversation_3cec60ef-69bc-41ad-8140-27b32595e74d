import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/utils/preferences.dart';
import 'package:wesell/data/models/user_model.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final List<MenuItem> menuItems;
  User? user; 
  @override
  Widget build(BuildContext context) {
     return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.settings),
        elevation: 0,
      ),
      body: ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];
        return ListTile(
          title: Text(
            item.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.blackTextTheme,
            ),
          ),
          trailing: const Icon(Icons.chevron_right, color: Colors.black54),
          onTap: item.onTap,
          contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
        );
      },
    ),
    );
  }

  
  @override
  void initState() {
    super.initState();
    _loadUserData();
    menuItems = [
      MenuItem(title: AppStrings.profile, onTap: profile),
      MenuItem(title: AppStrings.disputes),
      //MenuItem(title: AppStrings.settings), // its a usable code we will use it in future 
      MenuItem(title: AppStrings.logout, onTap: logout),
    ];
  }

   Future<void> _loadUserData() async {
    user = await Preferences.getUser() as User;
  }

  void logout(){
    Preferences.clearTokens();
    AppRoutes.pushRoot(context, AppRoutes.login);
  }

  void profile(){
    if(user != null && user!.role == 'merchant'){
      AppRoutes.pushScreen(context, AppRoutes.profileMarchant);
    }
    
  }
}

class MenuItem {
  final String title;
  final VoidCallback? onTap;

  MenuItem({required this.title, this.onTap});
}
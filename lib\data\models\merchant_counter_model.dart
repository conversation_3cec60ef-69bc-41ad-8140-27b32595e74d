class MerchantStats {
  final String from;
  final String to;
  final dynamic expenses;
  final int postedJobs;
  final int offerings;
  final int sellers;
  final dynamic revenue;
  final int buyers;
  final String duration;

  MerchantStats({
    required this.from,
    required this.to,
    required this.expenses,
    required this.postedJobs,
    required this.offerings,
    required this.sellers,
    required this.revenue,
    required this.buyers,
    required this.duration,
  });

  factory MerchantStats.fromJson(Map<String, dynamic> json) {
    return MerchantStats(
      from: json['from'] ?? '',
      to: json['to'] ?? '',
      expenses: json['expenses'] ?? 0,
      postedJobs: json['postedJobs'] ?? 0,
      offerings: json['offerings'] ?? 0,
      sellers: json['sellers'] ?? 0,
      revenue: json['revenue'] ?? 0,
      buyers: json['buyers'] ?? 0,
      duration: json['duration'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'from': from,
      'to': to,
      'expenses': expenses,
      'postedJobs': postedJobs,
      'offerings': offerings,
      'sellers': sellers,
      'revenue': revenue,
      'buyers': buyers,
      'duration': duration,
    };
  }
}

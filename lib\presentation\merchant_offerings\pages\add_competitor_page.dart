import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/data/models/create_offering_form_model.dart';

class AddCompetitorPage extends StatefulWidget {
  final CreateOfferingFormData formData;
  final CompetitorFormData? competitorToEdit;
  final int? editIndex;

  const AddCompetitorPage({
    super.key,
    required this.formData,
    this.competitorToEdit,
    this.editIndex,
  });

  @override
  State<AddCompetitorPage> createState() => _AddCompetitorPageState();
}

class _AddCompetitorPageState extends State<AddCompetitorPage> {
  final _formKey = GlobalKey<FormState>();
  final _competitorNameController = TextEditingController();
  final _notesController = TextEditingController();
  final _linkController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.competitorToEdit != null) {
      _initializeEditMode();
    }
  }

  void _initializeEditMode() {
    final competitor = widget.competitorToEdit!;
    _competitorNameController.text = competitor.competitorName;
    _notesController.text = competitor.notes;
    _linkController.text = competitor.link;
  }

  void _saveCompetitor() {
    if (_formKey.currentState?.validate() ?? false) {
      final competitor = CompetitorFormData(
        competitorName: _competitorNameController.text.trim(),
        notes: _notesController.text.trim(),
        link: _linkController.text.trim(),
      );

      if (widget.editIndex != null) {
        final modifiableList = [...widget.formData.competitors];// create a mutable copy
        modifiableList[widget.editIndex!] = competitor;
        widget.formData.competitors = modifiableList;
      } else {
        // Add new competitor
        final modifiableList = [...widget.formData.competitors]; // create a mutable copy
        modifiableList.add(competitor);
        widget.formData.competitors = modifiableList;
      }
      Navigator.pop(context, true); // Return true to indicate success
    }
  }

  void _cancel() {
    Navigator.pop(context, false);
  }

  @override
  void dispose() {
    _competitorNameController.dispose();
    _notesController.dispose();
    _linkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        foregroundColor: Colors.black,
        title: const Text('Add a competitor'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField(
                controller: _competitorNameController,
                hintText: AppStrings.competitorName,
                validator: (value) => Validators.validateRequired(value, AppStrings.competitorName),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _notesController,
                hintText: AppStrings.notes,
                validator: (value) => Validators.validateRequired(value, AppStrings.notes),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: _linkController,
                hintText: 'Enter competitor link',
                keyboardType: TextInputType.url,
                validator: (value) => Validators.validateUrl(value),
              ),
              const SizedBox(height: 32),
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Cancel',
                      onPressed: _cancel,
                      backgroundColor: Colors.grey.shade300,
                      textColor: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: 'Save',
                      onPressed: _saveCompetitor,
                      backgroundColor: AppColors.primaryTheme,
                      textColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

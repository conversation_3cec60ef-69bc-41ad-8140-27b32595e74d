import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../repositories/merchant_offering_repository.dart';

class CreateOfferingUseCase {
  final MerchantOfferingRepository repository;

  CreateOfferingUseCase(this.repository);

  Future<Either<Failure, CreateOfferingResponse>> call(Map<String, dynamic> offeringData) async {
    return await repository.createOffering(offeringData);
  }
}

class CreateOfferingResponse {
  final String id;
  final String message;

  CreateOfferingResponse({
    required this.id,
    required this.message,
  });

  factory CreateOfferingResponse.fromJson(Map<String, dynamic> json) {
    return CreateOfferingResponse(
      id: json['id'] ?? '',
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
    };
  }
}

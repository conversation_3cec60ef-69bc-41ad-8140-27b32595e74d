import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateBankDetailsUseCase {
  final ProfileMarchantRepository repository;

  UpdateBankDetailsUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateBankDetailsParams params) async {
    return await repository.updateBankDetails(
      iban: params.iban,
      accountName: params.accountName,
      bankName: params.bankName,
      ibanCertificate: params.ibanCertificate,
    );
  }
}

class UpdateBankDetailsParams {
  final String iban;
  final String accountName;
  final String bankName;
  final String ibanCertificate;

  UpdateBankDetailsParams({
    required this.iban,
    required this.accountName,
    required this.bankName,
    required this.ibanCertificate,
  });
}

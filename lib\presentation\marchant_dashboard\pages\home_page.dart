import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/constants/img_string.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/services/helper_services.dart';
import 'package:wesell/core/utils/preferences.dart';
import 'package:wesell/data/models/merchant_counter_model.dart';
import 'package:wesell/data/models/user_model.dart';
import 'package:wesell/presentation/marchant_dashboard/blocs/marchant_counter/merchant_counter_bloc.dart' hide GetActivitiesEvent;
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/dashboard_card.dart';
import '../../../core/localization/app_localizations.dart';
import '../../widgets/custom_shimmer_animation.dart';
import '../blocs/acticity/activity_bloc.dart';
import 'package:intl/intl.dart';


class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  User? user;
  ValueNotifier<bool> valueListenable = ValueNotifier(false);
  final List<String> _filterValues = ['Last 7 days','Today','Last month','Last year'];
  String _selectedDuration = 'last 7 days';

  @override
  void initState() {
    super.initState();
    _loadUserData();
    context.read<MerchantStatsBloc>().add(GetMerchantStatsEvent(duration: _selectedDuration));
    context.read<ActivityBloc>().add(GetActivitiesEvent());
  }

  Future<void> _loadUserData() async {
    user = await Preferences.getUser() as User;
    valueListenable.value = true;
  }

  String _mapFilterToDuration(String filter) {
    switch (filter) {
      case 'Today':
        return 'last 1 days';
      case 'Last 7 days':
        return 'last 7 days';
      case 'Last month':
        return 'last 1 months';
      case 'Last year':
        return 'last 1 years';
      default:
        return 'last 7 days';
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return ValueListenableBuilder(
      valueListenable: valueListenable,
      builder: (context, value, child) {
        return Scaffold(
          backgroundColor: AppColors.backgroundTheme,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: Text(localizations.home),
            foregroundColor: Colors.black,
            elevation: 0,),
          body: user?.role != 'merchant' ?  Center(child: Text('Home'))  :dashboardBody() ,
        );
      }
    );
  }

  Widget dashboardBody(){
    return CustomScrollView(
      slivers: [
        SliverAppBar(
        pinned: true,
        flexibleSpace: Container(
          color: AppColors.whiteTheme,
          child: Padding(
            padding: const EdgeInsets.only(right:15.0,left: 15.0,bottom: 10),
            child: CustomDropdownFormField<String>(
              focusedBorderColor: AppColors.borderTheme,
              contentPadding: EdgeInsets.symmetric(vertical: 12,horizontal: 16),
              borderRadius: 8,
              value: _filterValues.firstWhere(
                (filter) => _mapFilterToDuration(filter) == _selectedDuration,
                orElse: () => _filterValues[0],
              ),
              items: _filterValues,
              hintText: AppStrings.selectFilter,
              itemLabel: (item) => item,
              onChanged: (val) {
                final duration = _mapFilterToDuration(val!);
                setState(() => _selectedDuration = duration);
                context.read<MerchantStatsBloc>().add(GetMerchantStatsEvent(duration: duration));
              },
            ),
          ),
        ),
      ),

      SliverToBoxAdapter(
        child: counterCard(),
      ),

      SliverToBoxAdapter(
        child: Container(
          color: AppColors.whiteTheme,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Recent Activity',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.w400,color: AppColors.textgreyTheme),
            ),
          ),
        ),
      ),

      BlocBuilder<ActivityBloc, ActivityState>(
        builder: (context, state) {
          if (state is ActivitiesLoading) {
            return SliverToBoxAdapter(
              child: const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: CircularProgressIndicator(color: AppColors.primaryTheme,),
                ),
              ),
            );
          }

          if (state is ActivitiesError) {
            return SliverToBoxAdapter(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(state.message),
                ),
              ),
            );
          }

          if (state is ActivitiesLoaded) {
            if(state.activities.isEmpty){
              return SliverToBoxAdapter(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Text('No Recent Activity'),
                  ),
                ),
              );
            }
            final activities = state.activities.map((activityModel) => Activity(
              userName: "${activityModel.doer?.firstName ?? ''} ${activityModel.doer?.lastName ?? ''}".trim(),
              action: activityModel.action ?? '',
              createdAt: DateTime.tryParse(activityModel.createdAt ?? '') ?? DateTime.now(),
              avatarId: activityModel.doer?.avatar ?? '',
              entityId: activityModel.entityId,
              entityType: activityModel.entityName,
            )).toList();

            return ActivityScreen(activities: activities);
          }

          return SliverToBoxAdapter(
              child: SizedBox.shrink(),
            );
        },
      ),
      
      ],
    );
  }

  Widget counterCard(){
    return BlocBuilder<MerchantStatsBloc, MerchantStatsState>(
      builder: (context, state) {
        if (state is MerchantStatsLoading) {
          return Shimmer(child: counterView(null));
        }
        
        if (state is MerchantStatsError) {
          return Center(child: Text(state.message));
        }

        if (state is MerchantStatsLoaded) {
          final stats = state.stats;
          return counterView(stats);
        }

        return const SizedBox.shrink();
      },
      
    );
  }

  Widget counterView( MerchantStats? stats){
    return SizedBox(
            height: 600,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: GridView.count(
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 1.05,
                children: [
                  DashboardStatCard(
                    value: stats?.revenue.toString(),
                    label: 'SAR',
                    subtitle: 'Total Revenue',
                    icon: ImageStrings.saru,
                    iconBgColor: AppColors.bageGreen,
                  ),
                  DashboardStatCard(
                    value: stats?.offerings.toString(),
                    label: 'Products',
                    subtitle: 'Total Products',
                    icon: ImageStrings.product,
                    iconBgColor: AppColors.bagePink,
                  ),
                  DashboardStatCard(
                    value: stats?.postedJobs.toString(),
                    label: 'Jobs',
                    subtitle: 'Total Posted Jobs',
                    icon: ImageStrings.jobsGrid,
                    iconBgColor: AppColors.bageBlue,
                  ),
                  DashboardStatCard(
                    value: stats?.sellers.toString(),
                    label: 'Sellers',
                    subtitle: 'Total Sellers',
                    icon: ImageStrings.sellerGrid,
                    iconBgColor: AppColors.bagePurple,
                  ),
                  DashboardStatCard(
                    value: stats?.buyers.toString(),
                    label: 'Buyers',
                    subtitle: 'Total Buyers',
                    icon: ImageStrings.buyer,
                    iconBgColor: AppColors.bageTeal,
                  ),
                  DashboardStatCard(
                    value: stats?.expenses.toString(),
                    label: 'SAR',
                    subtitle: 'Total Expenses',
                    icon: ImageStrings.sar,
                    iconBgColor: AppColors.bageRed,
                  ),
                ],
              ),
            ),
          );
  }

  @override
  void dispose() {
    valueListenable.dispose();
    super.dispose();
  }
}



class Activity {
  final String userName;
  final String action;
  final DateTime createdAt;
  final String avatarId;
  final String? entityId;
  final String? entityType;

  Activity({
    required this.userName,
    required this.action,
    required this.createdAt,
    required this.avatarId,
    this.entityId,
    this.entityType,
  });
}

class ActivityGroup {
  final String label;
  final List<Activity> activities;

  ActivityGroup({required this.label, required this.activities});
}

class ActivityScreen extends StatelessWidget {
  final List<Activity> activities;

  const ActivityScreen({super.key, required this.activities});

List<ActivityGroup> groupActivities(List<Activity> activities) {
  final now = DateTime.now();
  activities = activities.where((a) => a.createdAt.isBefore(now)).toList();

  final todayStart = DateTime(now.year, now.month, now.day);
  final yesterdayStart = todayStart.subtract(Duration(days: 1));
  final twoDaysAgoStart = todayStart.subtract(Duration(days: 2));
  final oneWeekAgo = now.subtract(Duration(days: 7));
  final oneMonthAgo = now.subtract(Duration(days: 30));
  final oneYearAgo = now.subtract(Duration(days: 365));

  List<ActivityGroup> groups = [];

  final todayList = activities.where((a) => a.createdAt.isAfter(todayStart)).toList();
  if (todayList.isNotEmpty) groups.add(ActivityGroup(label: "Today", activities: todayList));

  final yesterdayList = activities.where((a) =>
      a.createdAt.isAfter(yesterdayStart) &&
      a.createdAt.isBefore(todayStart)).toList();
  if (yesterdayList.isNotEmpty) groups.add(ActivityGroup(label: "Yesterday", activities: yesterdayList));

  final twoDaysAgoList = activities.where((a) =>
      a.createdAt.isAfter(twoDaysAgoStart) &&
      a.createdAt.isBefore(yesterdayStart)).toList();
  if (twoDaysAgoList.isNotEmpty) groups.add(ActivityGroup(label: "Two Days Ago", activities: twoDaysAgoList));

  final withinAWeek = activities.where((a) =>
      a.createdAt.isAfter(oneWeekAgo) &&
      a.createdAt.isBefore(twoDaysAgoStart)).toList();
  if (withinAWeek.isNotEmpty) groups.add(ActivityGroup(label: "Within a Week", activities: withinAWeek));

  final oneWeekAgoOrMore = activities.where((a) =>
      a.createdAt.isAfter(oneMonthAgo) &&
      a.createdAt.isBefore(oneWeekAgo)).toList();
  if (oneWeekAgoOrMore.isNotEmpty) groups.add(ActivityGroup(label: "One Week Ago or More", activities: oneWeekAgoOrMore));

  final oneMonthAgoList = activities.where((a) =>
      a.createdAt.isAfter(oneYearAgo) &&
      a.createdAt.isBefore(oneMonthAgo)).toList();
  if (oneMonthAgoList.isNotEmpty) groups.add(ActivityGroup(label: "One Month Ago", activities: oneMonthAgoList));

  final moreThanAMonth = activities.where((a) =>
      a.createdAt.isBefore(oneMonthAgo) &&
      a.createdAt.isAfter(oneYearAgo)).toList();
  if (moreThanAMonth.isNotEmpty) groups.add(ActivityGroup(label: "More Than a Month", activities: moreThanAMonth));

  final oneYearAgoList = activities.where((a) =>
      a.createdAt.year == oneYearAgo.year).toList();
  if (oneYearAgoList.isNotEmpty) groups.add(ActivityGroup(label: "One Year Ago", activities: oneYearAgoList));

  final moreThanAYearList = activities.where((a) =>
      a.createdAt.isBefore(oneYearAgo)).toList();
  if (moreThanAYearList.isNotEmpty) groups.add(ActivityGroup(label: "More Than a Year", activities: moreThanAYearList));

  return groups;
}



   @override
  Widget build(BuildContext context) {
    final grouped = groupActivities(activities);

    return SliverList(
        delegate: SliverChildBuilderDelegate(
          childCount: grouped.length,
        (context, groupIndex) {
          final group = grouped[groupIndex];
          return Container(
            color: AppColors.whiteTheme,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 20.0, top: 12.0, bottom: 8.0, right: 12.0),
                  child: Text(
                    group.label,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600,color: AppColors.blackTextTheme),
                  ),
                ),
                for (int i = 0; i < group.activities.length; i++)
                  Stack(
                    clipBehavior: Clip.none,
            
                    children: [
                      // Activity Tile
                      Padding(
                        padding: const EdgeInsets.only(left: 16.0, right: 12.0),
                        child: ListTile(
                          onTap: () {
                            // Handle activity tap
                            if(group.activities[i].entityId != null){
                              AppRoutes.pushScreen(context, AppRoutes.subscriptionPlan,arguments: '');
                            }
                          },
                          contentPadding: const EdgeInsets.only(left: 12, right: 8),
                          leading: ClipOval(
                            child: Image.network(
                              AppConstants.mediaBaseUrl + group.activities[i].avatarId,
                              width: 28,
                              height: 28,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 28,
                                  height: 28,
                                    color: AppColors.iconLightTheme,
                                    alignment: Alignment.center,
                                    child: Text(
                                      HelperServices.getInitialLetter(group.activities[i].userName),
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  );
                                },
                                loadingBuilder: (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Shimmer(
                                    child: Container(
                                      width: 28,
                                      height: 28,
                                      color: AppColors.iconLightTheme,
                                    ),
                                  ); // Loading state
                                },
                            ),
                          ),
                          title: Text(group.activities[i].userName ?? ""),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(group.activities[i].action ?? ""),
                              Text(
                                timeAgo(group.activities[i].createdAt),
                                style: const TextStyle(color: Colors.grey, fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        left: i != group.activities.length - 1 ? 42 : 0,
                        top: 65,
                        bottom: 0,
                        child: Container(
                          width: 2,
                          color: i != group.activities.length - 1  ? Colors.grey.shade300: Colors.transparent,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          );
        },)
      );
  }

  String timeAgo(DateTime date) {
    final duration = DateTime.now().difference(date);
    if (duration.inMinutes < 60) return '${duration.inMinutes} minutes ago';
    if (duration.inHours < 24) return '${duration.inHours} hours ago';
    if (duration.inDays < 7) return '${duration.inDays} days ago';
    return DateFormat('dd MMM yyyy').format(date);
  }
}



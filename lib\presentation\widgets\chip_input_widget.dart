import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';

class ChipInputWidget extends StatefulWidget {
  final String label;
  final String hintText;
  final List<String> items;
  final Function(List<String>) onChanged;
  final String? Function(String?)? emptyValidator;
  final String? Function(List<String>)? validator;
  final bool required;

  const ChipInputWidget({
    super.key,
    required this.label,
    required this.hintText,
    required this.items,
    required this.onChanged,
    this.validator,
    this.emptyValidator,
    this.required = false,
  });

  @override
  State<ChipInputWidget> createState() => _ChipInputWidgetState();
}

class _ChipInputWidgetState extends State<ChipInputWidget> {
  final TextEditingController _controller = TextEditingController();
  String? _errorText;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _addItem() {
    final text = _controller.text.trim();
    if (text.isNotEmpty && !widget.items.contains(text)) {
      final updatedItems = [...widget.items, text];
      widget.onChanged(updatedItems);
      _controller.clear();
      _validateItems(updatedItems);
    }
  }

  void _removeItem(String item) {
    final updatedItems = widget.items.where((i) => i != item).toList();
    widget.onChanged(updatedItems);
    _validateItems(updatedItems);
  }

  void _validateItems(List<String> items) {
    if (widget.validator != null) {
      setState(() {
        _errorText = widget.validator!(items);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        
        // Input Field
        TextFormField(
          validator: widget.emptyValidator,
          controller: _controller,
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: TextStyle(
              color: AppColors.textPlaceholderTheme,
            ),
            suffixIcon: IconButton(
              icon: const Icon(Icons.add, color: AppColors.primaryTheme),
              onPressed: _addItem,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.borderTheme,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppColors.primaryTheme,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.errorTheme,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.errorTheme,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppColors.whiteTheme,
            errorMaxLines: 3,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            errorText: _errorText,
          ),
          onFieldSubmitted: (_) => _addItem(),
          textInputAction: TextInputAction.done,
        ),
        
        // Chips Display
        if (widget.items.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: widget.items.map((item) {
              return Chip(
                label: Text(
                  item,
                  style: const TextStyle(
                    color: AppColors.whiteTheme,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                backgroundColor: AppColors.primaryTheme,
                deleteIcon: const Icon(
                  Icons.close,
                  color: AppColors.whiteTheme,
                  size: 18,
                ),
                onDeleted: () => _removeItem(item),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}
